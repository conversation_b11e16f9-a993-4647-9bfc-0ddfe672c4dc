import { createOptimizer } from '../src/index.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * 数据优化脚本
 * 预处理Figma数据，生成优化后的数据文件
 */
async function optimizeData() {
  console.log('🚀 Starting data optimization...\n');

  try {
    // 1. 创建优化器
    const optimizer = createOptimizer({
      enableCache: false, // 构建时不需要缓存
      enableCompression: true
    });

    // 2. 加载原始数据
    console.log('📊 Loading original data...');
    const startTime = Date.now();
    await optimizer.initialize('./figma.yml');
    const loadTime = Date.now() - startTime;
    console.log(`✅ Data loaded in ${loadTime}ms\n`);

    // 3. 获取统计信息
    console.log('📈 Analyzing data...');
    const stats = optimizer.getOptimizationStats();
    console.log('Data Statistics:');
    console.log(`  Total components: ${stats.dataStats.totalComponents}`);
    console.log(`  Total component sets: ${stats.dataStats.totalComponentSets}`);
    console.log(`  Total nodes: ${stats.dataStats.totalNodes}`);
    console.log(`  Indexed properties: ${stats.dataStats.indexedProperties}`);
    console.log();

    // 4. 生成优化后的数据
    console.log('⚡ Generating optimized data...');
    
    // 导出不同格式的数据
    const formats = [
      { name: 'full', includeNodes: true, compress: false },
      { name: 'compressed', includeNodes: true, compress: true },
      { name: 'metadata-only', includeNodes: false, compress: false },
      { name: 'metadata-compressed', includeNodes: false, compress: true }
    ];

    const outputDir = './dist';
    await ensureDir(outputDir);

    for (const format of formats) {
      console.log(`  Generating ${format.name} format...`);
      
      const exportData = optimizer.exportOptimizedData({
        includeNodes: format.includeNodes,
        compress: format.compress
      });

      const filename = `figma-data-${format.name}.json`;
      const filepath = path.join(outputDir, filename);
      
      await fs.writeFile(filepath, JSON.stringify(exportData, null, 2));
      
      const fileStats = await fs.stat(filepath);
      const fileSizeKB = Math.round(fileStats.size / 1024);
      
      console.log(`    ✅ ${filename} (${fileSizeKB}KB)`);
      
      if (exportData.compressionStats) {
        console.log(`       Compression: ${exportData.compressionStats.compressionRatio.toFixed(2)}%`);
      }
    }
    console.log();

    // 5. 生成索引文件
    console.log('📋 Generating index files...');
    
    // 组件索引
    const componentIndex = {};
    for (const [key, value] of optimizer.parser.componentIndex.entries()) {
      if (typeof value === 'object' && !Array.isArray(value)) {
        componentIndex[key] = {
          id: value.id,
          name: value.name,
          componentSetId: value.componentSetId,
          properties: value.parsedProperties || {}
        };
      }
    }
    
    await fs.writeFile(
      path.join(outputDir, 'component-index.json'),
      JSON.stringify(componentIndex, null, 2)
    );
    console.log('  ✅ component-index.json');

    // 属性索引
    const propertyIndex = {};
    for (const [key, components] of optimizer.parser.propertyIndex.entries()) {
      propertyIndex[key] = components.map(comp => ({
        id: comp.id,
        name: comp.name
      }));
    }
    
    await fs.writeFile(
      path.join(outputDir, 'property-index.json'),
      JSON.stringify(propertyIndex, null, 2)
    );
    console.log('  ✅ property-index.json');

    // 6. 生成查询快捷方式
    console.log('\n🔍 Generating query shortcuts...');
    
    const shortcuts = {
      buttonSizes: await optimizer.getButtonSizes(),
      buttonColors: await optimizer.getButtonColors(),
      componentStats: await optimizer.getComponentStats()
    };
    
    await fs.writeFile(
      path.join(outputDir, 'query-shortcuts.json'),
      JSON.stringify(shortcuts, null, 2)
    );
    console.log('  ✅ query-shortcuts.json');

    // 7. 生成优化报告
    console.log('\n📊 Generating optimization report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      originalDataPath: './figma.yml',
      optimizationStats: stats,
      generatedFiles: formats.map(f => `figma-data-${f.name}.json`),
      indexFiles: ['component-index.json', 'property-index.json', 'query-shortcuts.json'],
      recommendations: generateRecommendations(stats)
    };
    
    await fs.writeFile(
      path.join(outputDir, 'optimization-report.json'),
      JSON.stringify(report, null, 2)
    );
    console.log('  ✅ optimization-report.json');

    // 8. 显示总结
    console.log('\n🎉 Data optimization completed!');
    console.log('\nGenerated files:');
    
    const distFiles = await fs.readdir(outputDir);
    for (const file of distFiles) {
      const filePath = path.join(outputDir, file);
      const fileStats = await fs.stat(filePath);
      const fileSizeKB = Math.round(fileStats.size / 1024);
      console.log(`  📄 ${file} (${fileSizeKB}KB)`);
    }

    console.log('\n💡 Usage recommendations:');
    console.log('  - Use compressed formats for production');
    console.log('  - Use metadata-only for lightweight queries');
    console.log('  - Use index files for fast lookups');
    console.log('  - Check optimization-report.json for detailed analysis');

    // 清理
    optimizer.destroy();

  } catch (error) {
    console.error('❌ Data optimization failed:', error);
    process.exit(1);
  }
}

/**
 * 确保目录存在
 */
async function ensureDir(dirPath) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

/**
 * 生成优化建议
 */
function generateRecommendations(stats) {
  const recommendations = [];

  if (stats.dataStats.totalComponents > 1000) {
    recommendations.push('Consider using component filtering for large datasets');
  }

  if (stats.dataStats.totalNodes > 5000) {
    recommendations.push('Use metadata-only format for faster loading');
  }

  if (stats.loadTime > 1000) {
    recommendations.push('Enable compression to reduce loading time');
  }

  if (stats.dataStats.indexedProperties < stats.dataStats.totalComponents * 0.5) {
    recommendations.push('Consider improving property extraction for better searchability');
  }

  return recommendations;
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  optimizeData();
}

export { optimizeData };
