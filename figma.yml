metadata:
  name: bilibili UIkit
  lastModified: '2025-07-11T03:57:54Z'
  thumbnailUrl: >-
    https://s3-alpha.figma.com/thumbnails/a3433004-03c7-4ef4-80db-d94883a76617?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCWOTIVFC2%2F20250810%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250810T120000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=469e69c628f72d0aab195700299cf83c1b27453848d2de38a9bf22da60144b0a
  components:
    '235:933':
      id: '235:933'
      key: a1ca792630b116928edd815576068a424ff10aca
      name: 功能布局=右侧三图标
      componentSetId: '235:813'
    '219:174':
      id: '219:174'
      key: 32e1c429390b2b44d0e085b3e379e926b7402fb1
      name: 尺寸=中按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '21821:43827':
      id: '21821:43827'
      key: 1af95b03f00a4ee07aae34d08a35bf2fc6254c4f
      name: 方向=┻, 样式=填充
      componentSetId: '21821:43822'
    '21821:43825':
      id: '21821:43825'
      key: 45138bd760594af4fe69ab20d04171177ff295b3
      name: 方向=┣, 样式=填充
      componentSetId: '21821:43822'
    '21821:43831':
      id: '21821:43831'
      key: 1f7cf85d6cf193b8421dffd76061d8467d420c53
      name: 方向=none, 样式=填充
      componentSetId: '21821:43822'
    '154:35175':
      id: '154:35175'
      key: 391470ec8e53990f3377b139bba9ffd09178c39e
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '219:152':
      id: '219:152'
      key: 3faabb588c63911135047d839913a993d9a4c533
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=线框, 状态=正常
      componentSetId: '154:35174'
    '17682:42608':
      id: '17682:42608'
      key: 97a1742586a92dd46b3bfcc741399d7b7854b18b
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=浅底, 状态=正常
      componentSetId: '154:35174'
    '17656:33781':
      id: '17656:33781'
      key: 8715788ccbe36ef2a09b449ecae1e8a100badede
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=按下
      componentSetId: '154:35174'
    '17656:34987':
      id: '17656:34987'
      key: c7f6c93d213da4853167d46fbe718224a0a0e37a
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=禁用
      componentSetId: '154:35174'
    '154:35236':
      id: '154:35236'
      key: 2efd42f1b86b6e6b3f1593e5f8b52585a030eeb1
      name: 尺寸=小按钮, 推荐宽度=短, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '154:35249':
      id: '154:35249'
      key: b7e5c6822ebc026fcd157ac43298c325d92dfadb
      name: 尺寸=小按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '154:35337':
      id: '154:35337'
      key: b89f759c3411a061a96f89881147e04c1ff40736
      name: 尺寸=小按钮, 推荐宽度=短, 颜色=黄(付费通知), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '17682:43027':
      id: '17682:43027'
      key: 6891e40631c033fb5194beda00358c8ed5a912cd
      name: 尺寸=小按钮, 推荐宽度=短, 颜色=蓝(下载、游戏等), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '154:35369':
      id: '154:35369'
      key: aff5c211469e95c89a0b4e16324feb470300e103
      name: 尺寸=小按钮, 推荐宽度=短, 颜色=灰, 样式=浅底, 状态=正常
      componentSetId: '154:35174'
    '7451:44850':
      id: '7451:44850'
      key: 46cd62df852fc70b9e837cb19e7a50a0e6ad9226
      name: 尺寸=小按钮, 推荐宽度=短, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '219:246':
      id: '219:246'
      key: 9d573a660388e5e0e5352a810292f44ebb4c4f53
      name: 尺寸=大按钮, 推荐宽度=中, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '303:1662':
      id: '303:1662'
      key: 611a743a1da3343b7ef0d9e6b5537c4d1e453d46
      name: 颜色=粉（默认）, 状态=正常
      componentSetId: '2533:4941'
    '21821:43759':
      id: '21821:43759'
      key: bdd3b43aa54e5bd510a4a9fe358db495deecdad7
      name: 方向=┣, 背景=off
      componentSetId: '21821:43755'
    '7451:44848':
      id: '7451:44848'
      key: 15054f6871f2f120385ed03bd0cd9d605cd709ec
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '7451:44852':
      id: '7451:44852'
      key: 42aa990749263e5d1727eeb491bf9c3345839993
      name: 01 控件/按钮Button/小按钮/长/白/实色/正常
    '21821:43761':
      id: '21821:43761'
      key: bcb2aecc9a9bd9fa5aa25568446aa3985f01c784
      name: 方向=┫, 背景=off
      componentSetId: '21821:43755'
    '21821:43757':
      id: '21821:43757'
      key: 23cddc3312022b626b08dd6f4070d2c26b07df0d
      name: 方向=┳, 背景=off
      componentSetId: '21821:43755'
    '21821:43763':
      id: '21821:43763'
      key: c1394221175776614f3ca6ef9a1c8fa56fb78329
      name: 方向=┻, 背景=off
      componentSetId: '21821:43755'
    '19278:40596':
      id: '19278:40596'
      key: b5e5c7d69f0afb0ab6f3b54b791aaca78592078a
      name: 类型=容器
      componentSetId: '19278:40591'
    '19278:40592':
      id: '19278:40592'
      key: 3e787ecc84147f660175154faccbefe8a28bd68f
      name: 类型=占位
      componentSetId: '19278:40591'
    '19278:40595':
      id: '19278:40595'
      key: c59a590cd4c1a0f277a09dcc503b843d1a6b5d98
      name: 类型=间距左右
      componentSetId: '19278:40591'
    '21821:43423':
      id: '21821:43423'
      key: eead8c65291b0b995a30498a10529601774a7109
      name: 方向=┣┓, 背景=off, 状态=默认说明
      componentSetId: '21821:43378'
    '21821:43447':
      id: '21821:43447'
      key: 09f6cce647b02c8b18146adcb04bd1b85466f3f7
      name: 方向=┗┫, 背景=off, 状态=默认说明
      componentSetId: '21821:43378'
    '7451:44854':
      id: '7451:44854'
      key: b2a877d48ac892263cadd6c3a4c3ba92a8dbf163
      name: 尺寸=中按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '7451:44856':
      id: '7451:44856'
      key: 0c2963a5751b1638c5f915849352bbdd1c004f36
      name: 尺寸=中按钮, 推荐宽度=短, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '7451:44858':
      id: '7451:44858'
      key: 0523d985d9fd3b96300ea6b5d816a35f36ba9be3
      name: 尺寸=中按钮, 推荐宽度=长, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '21821:43391':
      id: '21821:43391'
      key: c600cd9dcdf61de3056edde5f58aa94dc3355bec
      name: 方向=┣, 背景=off, 状态=默认说明
      componentSetId: '21821:43378'
    '7451:44860':
      id: '7451:44860'
      key: 1ab45f62f12c27bbab8054ec4d1b1bd1f0e0894b
      name: 尺寸=大按钮, 推荐宽度=中, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '7451:44862':
      id: '7451:44862'
      key: 21a984ca9300db2432f03d7f59f277c0cf13358e
      name: 尺寸=大按钮, 推荐宽度=长, 颜色=白, 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '19278:40594':
      id: '19278:40594'
      key: 41ac9163171bca00c25b21c34b0da58f589a5961
      name: 类型=间距上下
      componentSetId: '19278:40591'
    '144:1':
      id: '144:1'
      key: 27d393b85abd1bd1c201de56cac4b0bf08662737
      name: 类型=竖屏, 深浅色=light
      componentSetId: '144:9'
    '219:248':
      id: '219:248'
      key: 0bdbb65f21991fc2004957188e2b44e1758431f3
      name: 尺寸=大按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '3121:5266':
      id: '3121:5266'
      key: 0dfb3998481260b6c0004ddaa359e26504196236
      name: 类型=默认, 深色模式=false
      componentSetId: '3121:5269'
    '16530:67494':
      id: '16530:67494'
      key: 53cca2af7f404817fe78e1914f89e8750daa439d
      name: 类型=UP主
      componentSetId: '19526:38963'
    '219:178':
      id: '219:178'
      key: d9e3e588d71f2cbaa326d560040290e98e1baca4
      name: 尺寸=中按钮, 推荐宽度=长, 颜色=粉(默认), 样式=实色, 状态=正常
      componentSetId: '154:35174'
    '219:190':
      id: '219:190'
      key: 5f71ec9f6fafe7e4ed8ce6d7f150b791dea2cc7d
      name: 尺寸=中按钮, 推荐宽度=长, 颜色=灰, 样式=浅底, 状态=正常
      componentSetId: '154:35174'
    '154:35375':
      id: '154:35375'
      key: a48ba3749dfb92f19696e0192c451313f42c15cf
      name: 尺寸=小按钮, 推荐宽度=中, 颜色=灰, 样式=浅底, 状态=正常
      componentSetId: '154:35174'
    '303:1666':
      id: '303:1666'
      key: 4641002b47837c9dc6506bfd735991fb317bccad
      name: 01 控件/按钮·纯文字/灰/无
  componentSets:
    '235:813':
      id: '235:813'
      key: abd8f6ea55bdbcd8f774dccb4e1adc7c7d179fdd
      name: 02 Bar/顶部栏 Top navbar·图片型
      description: |-
        顶部栏图片型 Top navbar_photo
        顶栏叠层图片时选用
    '154:35174':
      id: '154:35174'
      key: 38e67d8c31457c5a411e8c8ee775a7f3aeacd6f3
      name: 01 控件/按钮Button
      description: ' 按钮 Button'
    '21821:43822':
      id: '21821:43822'
      key: 9a6f30733436f290ef3f034693f6aded89fd0c46
      name: 序号
      description: ''
    '2533:4941':
      id: '2533:4941'
      key: 9cee4223ae488fa33f249c2403658670fa40e6e1
      name: 01 控件/按钮Button·纯文字
      description: ' 按钮 Button'
    '21821:43755':
      id: '21821:43755'
      key: e991c501fb3e41d63ee56610d3f35ab4e87fad7f
      name: 尺寸
      description: ''
    '19278:40591':
      id: '19278:40591'
      key: ae2ce69ddf0dd92c416df427976bd0a556e30d05
      name: 遮罩
      description: ''
    '21821:43378':
      id: '21821:43378'
      key: 8ea2ed7d701c4f475d68f3be9705d2a4bc9fe937
      name: 说明
      description: ''
    '144:9':
      id: '144:9'
      key: 5616f8bd6bdab907ee92f69624f8d97cdcc5c251
      name: 00 系统/手势条 Home Indicator
      description: 手势条 Home Indicator
    '3121:5269':
      id: '3121:5269'
      key: 4d63e4e38c7fa664752cb352aabad2ec0aef6755
      name: 04 状态/页面状态Page status
      description: ''
    '19526:38963':
      id: '19526:38963'
      key: 2203a300d933206a25c2aed724444b16740a2400
      name: 提示栏图片
      description: ''
nodes:
  - id: '17656:39309'
    name: 按钮
    type: FRAME
    layout: layout_08VSAW
    fills: fill_JL4PA8
    children:
      - id: '17656:39310'
        name: 概述
        type: TEXT
        layout: layout_OFAO97
        text: 概述
        textStyle: style_7ZURI7
        fills: fill_DXAX1L
      - id: '17656:39311'
        name: 按钮用于触发操作或提交表单数据，可供用户单击或触摸，具有文本标签或图标以表示其目的
        type: TEXT
        layout: layout_0Z2RXU
        text: 按钮用于触发操作或提交表单数据，可供用户单击或触摸，具有文本标签或图标以表示其目的
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:39312'
        name: Frame 1
        type: FRAME
        layout: layout_BQN39O
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17656:39313'
            name: iPhone 11 Pro / X - 3
            type: FRAME
            layout: layout_RYZO3O
            fills: fill_JL4PA8
            borderRadius: 32px 32px 0px 0px
            children:
              - id: '17656:40972'
                name: Rectangle 35
                type: RECTANGLE
                layout: layout_QCIXH3
                fills: fill_NKLVEI
              - id: '17656:40973'
                name: 02 Bar/顶部栏 Top navbar·图片型
                type: INSTANCE
                layout: layout_ELCWTN
                fills: fill_SXJIAU
                componentId: '235:933'
                componentProperties:
                  - name: 功能布局
                    value: 右侧三图标
                    type: VARIANT
              - id: '17656:40974'
                name: Ellipse 3
                type: ELLIPSE
                layout: layout_5VPN03
                fills: fill_38XS4S
                strokes: stroke_G75BOA
              - id: '17656:40975'
                name: 关注模块
                type: GROUP
                layout: layout_HXUZHJ
                borderRadius: 0px 0px 0px 0px
              - id: '17656:41030'
                name: 这里是某个人编辑写的个人简介
                type: TEXT
                layout: layout_KPFBCS
                text: 这里是某个人编辑写的个人简介
                textStyle: style_UGRAWK
                fills: fill_HEWAVM
              - id: '17656:41031'
                name: 详情
                type: TEXT
                layout: layout_66D1OI
                text: 详情
                textStyle: style_UGRAWK
                fills: fill_Z0YS8Z
              - id: '17656:41032'
                name: Frame 33
                type: FRAME
                layout: layout_G9U7GL
      - id: '17656:39319'
        name: Rectangle 174
        type: RECTANGLE
        layout: layout_GEVQSY
        fills: fill_KPM4B0
      - id: '17656:39320'
        name: 规格
        type: TEXT
        layout: layout_394SWN
        text: 规格
        textStyle: style_7ZURI7
        fills: fill_DXAX1L
      - id: '17656:39321'
        name: 结构
        type: TEXT
        layout: layout_6G59DA
        text: 结构
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17656:39322'
        name: 按钮主要由文字、图标和背板组成
        type: TEXT
        layout: layout_8OXPMS
        text: 按钮主要由文字、图标和背板组成
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:39323'
        name: Frame 4
        type: FRAME
        layout: layout_J806XQ
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17656:41235'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_BQT8EC
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:174'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17656:41235;17656:30312
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17656:41251'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_WBU8YH
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:174'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17656:41251;17656:30312
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17656:41260'
            name: 序号
            type: INSTANCE
            layout: layout_LDKG31
            componentId: '21821:43827'
            componentProperties:
              - name: Text#748:11
                value: '4'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:41260;231:37
                name: Vector 1
                type: IMAGE-SVG
                layout: layout_SSX4OQ
                strokes: stroke_YCR7TL
              - id: I17656:41260;231:38
                name: Frame 104
                type: FRAME
                layout: layout_1JW1B5
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:41269'
            name: 序号
            type: INSTANCE
            layout: layout_OO9MFV
            componentId: '21821:43827'
            componentProperties:
              - name: Text#748:11
                value: '2'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:41269;231:37
                name: Vector 1
                type: IMAGE-SVG
                layout: layout_5DBE3T
                strokes: stroke_YCR7TL
              - id: I17656:41269;231:38
                name: Frame 104
                type: FRAME
                layout: layout_93FBVD
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:41273'
            name: 序号
            type: INSTANCE
            layout: layout_J0V9MW
            componentId: '21821:43827'
            componentProperties:
              - name: Text#748:11
                value: '1'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:41273;231:37
                name: Vector 1
                type: IMAGE-SVG
                layout: layout_SSX4OQ
                strokes: stroke_YCR7TL
              - id: I17656:41273;231:38
                name: Frame 104
                type: FRAME
                layout: layout_1JW1B5
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:39327'
            name: 序号
            type: INSTANCE
            layout: layout_94CD87
            componentId: '21821:43825'
            componentProperties:
              - name: Text#748:11
                value: '3'
                type: TEXT
              - name: 方向
                value: ┣
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:39327;231:33
                name: Vector 1
                type: IMAGE-SVG
                layout: layout_D82CAB
                strokes: stroke_YCR7TL
              - id: I17656:39327;231:34
                name: Frame 104
                type: FRAME
                layout: layout_B4TRM3
                fills: fill_WB2TH2
                borderRadius: 8px
      - id: '17656:39328'
        name: Group 34551744
        type: GROUP
        layout: layout_VTAADP
        children:
          - id: '17656:39329'
            name: 文字内容
            type: TEXT
            layout: layout_YO5L5B
            text: 文字内容
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17656:39330'
            name: 背板
            type: TEXT
            layout: layout_UIDU73
            text: 背板
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17656:39331'
            name: 左侧图标
            type: TEXT
            layout: layout_ISYHCR
            text: 左侧图标
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17656:41265'
            name: 右侧图标
            type: TEXT
            layout: layout_389EJD
            text: 右侧图标
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17656:39332'
            name: 序号
            type: INSTANCE
            layout: layout_2AL4CQ
            componentId: '21821:43831'
            componentProperties:
              - name: Text#748:11
                value: '1'
                type: TEXT
              - name: 方向
                value: none
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:39332;231:45
                name: Frame 104
                type: FRAME
                layout: layout_8T2CTJ
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:39333'
            name: 标注/
            type: INSTANCE
            layout: layout_4K9IB4
            componentId: '21821:43831'
            componentProperties:
              - name: Text#748:11
                value: '2'
                type: TEXT
              - name: 方向
                value: none
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:39333;231:45
                name: Frame 104
                type: FRAME
                layout: layout_8T2CTJ
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:39334'
            name: 标注/
            type: INSTANCE
            layout: layout_DQ5YVU
            componentId: '21821:43831'
            componentProperties:
              - name: Text#748:11
                value: '3'
                type: TEXT
              - name: 方向
                value: none
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:39334;231:45
                name: Frame 104
                type: FRAME
                layout: layout_8T2CTJ
                fills: fill_WB2TH2
                borderRadius: 8px
          - id: '17656:41266'
            name: 标注/
            type: INSTANCE
            layout: layout_IDKKMN
            componentId: '21821:43831'
            componentProperties:
              - name: Text#748:11
                value: '4'
                type: TEXT
              - name: 方向
                value: none
                type: VARIANT
              - name: 样式
                value: 填充
                type: VARIANT
            children:
              - id: I17656:41266;231:45
                name: Frame 104
                type: FRAME
                layout: layout_8T2CTJ
                fills: fill_WB2TH2
                borderRadius: 8px
      - id: '17656:39335'
        name: 类型与变体
        type: TEXT
        layout: layout_VIZG2V
        text: 类型与变体
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17656:39336'
        name: 不同类型与变体
        type: TEXT
        layout: layout_X7D9VL
        text: 不同类型与变体
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17671:41310'
        name: Frame 34551794
        type: FRAME
        layout: layout_70AQB0
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17671:41311'
            name: 有图标
            type: TEXT
            layout: layout_S5PD4I
            text: 有图标
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41312'
            name: 无图标
            type: TEXT
            layout: layout_5JH1MD
            text: 无图标
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41313'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_DSOIHC
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:41313;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:41955'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_I3K4FY
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:41955;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
      - id: '17656:39413'
        name: 图标
        type: TEXT
        layout: layout_W8NO4V
        text: 图标
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17656:39418'
        name: 按钮可配置图标以辅助和加强示意
        type: TEXT
        layout: layout_PR0JWW
        text: 按钮可配置图标以辅助和加强示意
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44702'
        name: Frame 34551798
        type: FRAME
        layout: layout_6L1TTE
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:44703'
            name: 实色
            type: TEXT
            layout: layout_I9JEZ3
            text: 实色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17682:44705'
            name: 线框
            type: TEXT
            layout: layout_SYZ9YZ
            text: 线框
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17682:44707'
            name: 浅底
            type: TEXT
            layout: layout_F7TGLN
            text: 浅底
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17682:44710'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_XC5099
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:44710;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:44764'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_QIUURD
            strokes: stroke_O86E6O
            borderRadius: 12px
            componentId: '219:152'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 线框
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:44764;17656:29006
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:44772'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_KTSQOB
            fills: fill_MGY4N5
            borderRadius: 12px
            componentId: '17682:42608'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:44772;17682:42609
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
      - id: '17682:44713'
        name: 样式
        type: TEXT
        layout: layout_VQ4NP5
        text: 样式
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17682:44714'
        name: 实色、线框、浅底，依次视觉强度和优先级递减
        type: TEXT
        layout: layout_684RBK
        text: 实色、线框、浅底，依次视觉强度和优先级递减
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17656:39362'
        name: Frame 34551784
        type: FRAME
        layout: layout_TXG6E0
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17671:41971'
            name: 正常
            type: TEXT
            layout: layout_I9JEZ3
            text: 正常
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41990'
            name: 按下
            type: TEXT
            layout: layout_SYZ9YZ
            text: 按下
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41999'
            name: 禁用
            type: TEXT
            layout: layout_F7TGLN
            text: 禁用
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41972'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_XC5099
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:41972;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:41991'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_QIUURD
            fills: fill_E1IK1A
            opacity: 0.699999988079071
            borderRadius: 12px
            componentId: '17656:33781'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 按下
                type: VARIANT
            children:
              - id: I17671:41991;17656:33782
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42000'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_KTSQOB
            fills: fill_E1IK1A
            opacity: 0.4000000059604645
            borderRadius: 12px
            componentId: '17656:34987'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 禁用
                type: VARIANT
            children:
              - id: I17671:42000;17656:34988
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
      - id: '17656:39414'
        name: 状态
        type: TEXT
        layout: layout_K711JM
        text: 状态
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17656:39419'
        name: 正常：默认正常状态
        type: TEXT
        layout: layout_FIV9TW
        text: 正常：默认正常状态
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42067'
        name: 按下：手指按下状态变化
        type: TEXT
        layout: layout_8XNAT3
        text: 按下：手指按下状态变化
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42068'
        name: 禁用：不可点击状态
        type: TEXT
        layout: layout_5ZBTFA
        text: 禁用：不可点击状态
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42195'
        name: Frame 34551796
        type: FRAME
        layout: layout_ARTAYG
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17671:42196'
            name: 短
            type: TEXT
            layout: layout_K94R0B
            text: 短
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42197'
            name: 中
            type: TEXT
            layout: layout_VJEAXM
            text: 中
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42198'
            name: 长
            type: TEXT
            layout: layout_RVMY55
            text: 长
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42199'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_RQANR8
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35236'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42199;17656:29319
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42201'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_0HQO1L
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42201;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42203'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_44JKAM
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35249'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42203;17656:30203
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
      - id: '17671:42205'
        name: 推荐宽度
        type: TEXT
        layout: layout_TEFKCE
        text: 推荐宽度
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17671:42206'
        name: 预设了几档默认宽度，尽可能规范使用，必要时也可拖动以自定义宽度
        type: TEXT
        layout: layout_D99C8G
        text: 预设了几档默认宽度，尽可能规范使用，必要时也可拖动以自定义宽度
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42473'
        name: 颜色
        type: TEXT
        layout: layout_YHD9ZG
        text: 颜色
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17671:42466'
        name: Frame 34551797
        type: FRAME
        layout: layout_HBVTKC
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17671:42467'
            name: 粉色
            type: TEXT
            layout: layout_FGOW6H
            text: 粉色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42573'
            name: 灰色
            type: TEXT
            layout: layout_FB71RQ
            text: 灰色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42468'
            name: 黄色
            type: TEXT
            layout: layout_EJTRKJ
            text: 黄色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42574'
            name: 白色
            type: TEXT
            layout: layout_JITIOI
            text: 白色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42469'
            name: 蓝色
            type: TEXT
            layout: layout_SYZ9YZ
            text: 蓝色
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42471'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_Q137T7
            fills: fill_WXPUWM
            borderRadius: 12px
            componentId: '154:35337'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 黄(付费通知)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42471;17656:29353
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42528'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_BD6SJK
            fills: fill_6JBSFX
            borderRadius: 12px
            componentId: '17682:43027'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 蓝(下载、游戏等)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42528;17682:43028
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42513'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_8Y224S
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35236'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42513;17656:29319
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42543'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_YUHSCZ
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35369'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42543;17656:29357
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42558'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_2V2E9U
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44850'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42558;17656:29375
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
      - id: '17671:42474'
        name: 不同操作类型有不同颜色的按钮来区分
        type: TEXT
        layout: layout_XLMKQT
        text: 不同操作类型有不同颜色的按钮来区分
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44869'
        name: 粉色：默认常用
        type: TEXT
        layout: layout_QNKJMX
        text: 粉色：默认常用
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44872'
        name: 白色：彩色或图片背景
        type: TEXT
        layout: layout_0PIP3P
        text: 白色：彩色或图片背景
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44870'
        name: 黄色：付费类
        type: TEXT
        layout: layout_KC628K
        text: 黄色：付费类
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44873'
        name: 灰色：弱化和不积极的情况
        type: TEXT
        layout: layout_4HXRQF
        text: 灰色：弱化和不积极的情况
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17682:44871'
        name: 蓝色：下载、游戏类
        type: TEXT
        layout: layout_7QA5CE
        text: 蓝色：下载、游戏类
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42085'
        name: Frame 34551795
        type: FRAME
        layout: layout_RJME43
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17671:42086'
            name: 小按钮
            type: TEXT
            layout: layout_42OXAN
            text: 小按钮
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42087'
            name: 中按钮
            type: TEXT
            layout: layout_JU9H2L
            text: 中按钮
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42088'
            name: 大按钮
            type: TEXT
            layout: layout_NTQQ2F
            text: 大按钮
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:42089'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_H13LTL
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42089;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:42091'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_T4W45A
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:174'
            componentProperties:
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42091;17656:30312
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17671:42093'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_3D1XUX
            fills: fill_E1IK1A
            borderRadius: 22px
            componentId: '219:246'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:42093;17656:31286
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
      - id: '17671:42095'
        name: 尺寸
        type: TEXT
        layout: layout_Y5WCVY
        text: 尺寸
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17671:42096'
        name: 小按钮：较小，比较常用，适用于空间较小的场景
        type: TEXT
        layout: layout_Y37DH9
        text: 小按钮：较小，比较常用，适用于空间较小的场景
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42097'
        name: 中按钮：若空间足够推荐使用该尺寸
        type: TEXT
        layout: layout_HSRYCL
        text: 中按钮：若空间足够推荐使用该尺寸
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17671:42098'
        name: 大按钮：页面级别的CTA按钮
        type: TEXT
        layout: layout_KIUKE5
        text: 大按钮：页面级别的CTA按钮
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17656:39337'
        name: Frame 34551782
        type: FRAME
        layout: layout_M5RUYG
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17656:39338'
            name: 普通按钮
            type: TEXT
            layout: layout_SSQ3VB
            text: 普通按钮
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17656:39339'
            name: 文字按钮
            type: TEXT
            layout: layout_CFE4EH
            text: 文字按钮
            textStyle: style_FU349H
            fills: fill_QB4BJQ
          - id: '17671:41280'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_8SCGP2
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:41280;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17671:41296'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_C7JJDF
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 右图标#17671:444
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17671:440
                value: 'false'
                type: BOOLEAN
              - name: 按钮文字#17671:436
                value: 按钮
                type: TEXT
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17671:41296;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
      - id: '17656:39412'
        name: 类型
        type: TEXT
        layout: layout_GYHMAL
        text: 类型
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17656:39417'
        name: 普通按钮：常用类型
        type: TEXT
        layout: layout_M40K47
        text: 普通按钮：常用类型
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17656:39425'
        name: 文字按钮：引导性很弱的时候使用
        type: TEXT
        layout: layout_COK5LW
        text: 文字按钮：引导性很弱的时候使用
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17656:39434'
        name: Frame 183
        type: FRAME
        layout: layout_AN1LJ9
        children:
          - id: '17656:39435'
            name: Rectangle 177
            type: RECTANGLE
            layout: layout_KBSJGI
            fills: fill_F7701Z
      - id: '17656:39482'
        name: Frame 332
        type: FRAME
        layout: layout_7IGNB3
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:44983'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_68J9DN
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:44983;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45000'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_U6TROF
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35236'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45000;17656:29319
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45015'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_91B0R5
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35249'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45015;17656:30203
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:44991'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_5ZTGT6
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:44991;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45030'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_JNS1WK
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'true'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45030;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45040'
            name: 标注/
            type: INSTANCE
            layout: layout_52FX5N
            fills: fill_JL4PA8
            componentId: '21821:43759'
            componentProperties:
              - name: Text#748:0
                value: '24'
                type: TEXT
              - name: 方向
                value: ┣
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45040;212:42
                name: Frame 54
                type: FRAME
                layout: layout_8GJU1V
              - id: I17682:45040;212:46
                name: Frame 54
                type: FRAME
                layout: layout_6QOFF5
      - id: '17682:45145'
        name: Frame 34551800
        type: FRAME
        layout: layout_GYDRQR
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:45146'
            name: 小按钮
            type: TEXT
            layout: layout_2P9XHW
            text: 小按钮
            textStyle: style_X1ZR25
            fills: fill_QB4BJQ
          - id: '17682:45147'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_68J9DN
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44848'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45147;17656:28795
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45148'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_HWX6RL
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44850'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45148;17656:29375
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45149'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_Z99SAC
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44852'
            componentProperties:
              - name: 左图标#17642:34
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:43
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:251
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:82
                value: 按钮
                type: TEXT
            children:
              - id: I17682:45149;17656:30215
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45150'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_5ZTGT6
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44848'
            componentProperties:
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45150;17656:28795
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45151'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_JNS1WK
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44848'
            componentProperties:
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45151;17656:28795
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:45152'
            name: 标注/
            type: INSTANCE
            layout: layout_Q5N13Y
            fills: fill_JL4PA8
            componentId: '21821:43761'
            componentProperties:
              - name: Text#748:0
                value: '24'
                type: TEXT
              - name: 方向
                value: ┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45152;212:50
                name: Frame 54
                type: FRAME
                layout: layout_AFC4EX
              - id: I17682:45152;212:54
                name: Frame 54
                type: FRAME
                layout: layout_QXNRKH
          - id: '17682:45303'
            name: 标注/
            type: INSTANCE
            layout: layout_UEJDWZ
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '6'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45303;212:35
                name: Frame 54
                type: FRAME
                layout: layout_5A8VRQ
              - id: I17682:45303;212:39
                name: Frame 103
                type: FRAME
                layout: layout_RRW5SY
                borderRadius: 4px
          - id: '17682:45602'
            name: 标注/
            type: INSTANCE
            layout: layout_N0R02A
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '72'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45602;212:58
                name: Frame 54
                type: FRAME
                layout: layout_U9NQD8
              - id: I17682:45602;212:62
                name: Frame 103
                type: FRAME
                layout: layout_H4I2EV
                borderRadius: 4px
          - id: '17682:45621'
            name: 标注/
            type: INSTANCE
            layout: layout_C6R8AT
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '56'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45621;212:58
                name: Frame 54
                type: FRAME
                layout: layout_OK8YMM
              - id: I17682:45621;212:62
                name: Frame 103
                type: FRAME
                layout: layout_HQ90QO
                borderRadius: 4px
          - id: '17682:45628'
            name: 标注/
            type: INSTANCE
            layout: layout_DRKPKF
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '94'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45628;212:58
                name: Frame 54
                type: FRAME
                layout: layout_KIU9RV
              - id: I17682:45628;212:62
                name: Frame 103
                type: FRAME
                layout: layout_A6ZI8J
                borderRadius: 4px
          - id: '17682:45343'
            name: 标注/
            type: INSTANCE
            layout: layout_A38503
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '6'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45343;212:35
                name: Frame 54
                type: FRAME
                layout: layout_5A8VRQ
              - id: I17682:45343;212:39
                name: Frame 103
                type: FRAME
                layout: layout_RRW5SY
                borderRadius: 4px
          - id: '17682:45401'
            name: 标注/
            type: INSTANCE
            layout: layout_A5I34N
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '2'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45401;212:58
                name: Frame 54
                type: FRAME
                layout: layout_H61HTJ
              - id: I17682:45401;212:62
                name: Frame 103
                type: FRAME
                layout: layout_GJ4QDW
                borderRadius: 4px
          - id: '17682:45425'
            name: 标注/
            type: INSTANCE
            layout: layout_AOWIG1
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45425;212:58
                name: Frame 54
                type: FRAME
                layout: layout_P1PCDE
              - id: I17682:45425;212:62
                name: Frame 103
                type: FRAME
                layout: layout_K6FDKZ
                borderRadius: 4px
          - id: '17682:45439'
            name: 标注/
            type: INSTANCE
            layout: layout_D9KUYS
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45439;212:58
                name: Frame 54
                type: FRAME
                layout: layout_B8RYTT
              - id: I17682:45439;212:62
                name: Frame 103
                type: FRAME
                layout: layout_V15JF0
                borderRadius: 4px
          - id: '17682:45432'
            name: 标注/
            type: INSTANCE
            layout: layout_IY7BSD
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+2
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45432;212:58
                name: Frame 54
                type: FRAME
                layout: layout_Q9M1FY
              - id: I17682:45432;212:62
                name: Frame 103
                type: FRAME
                layout: layout_BU3MR5
                borderRadius: 4px
          - id: '17682:45440'
            name: 标注/
            type: INSTANCE
            layout: layout_6OEO70
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+2
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45440;212:58
                name: Frame 54
                type: FRAME
                layout: layout_EVUA3K
              - id: I17682:45440;212:62
                name: Frame 103
                type: FRAME
                layout: layout_KEHL2O
                borderRadius: 4px
          - id: '17682:45197'
            name: 标注/
            type: INSTANCE
            layout: layout_JEW9II
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45197;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MRHKKH
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45327'
            name: 标注/
            type: INSTANCE
            layout: layout_SX8KOS
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45327;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_CHKAGM
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45329'
            name: 标注/
            type: INSTANCE
            layout: layout_LZSQ2I
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45329;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_CVCTV9
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45323'
            name: 标注/
            type: INSTANCE
            layout: layout_KZ8308
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45323;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MRHKKH
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45337'
            name: 标注/
            type: INSTANCE
            layout: layout_CZ7F78
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45337;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MRHKKH
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45199'
            name: 标注/
            type: INSTANCE
            layout: layout_FBJPB7
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45199;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '19274:38249'
            name: 标注/
            type: INSTANCE
            layout: layout_NPMG1V
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:38249;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_QM4EDW
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '19274:38252'
            name: 标注/
            type: INSTANCE
            layout: layout_JJCEPP
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:38252;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_QM4EDW
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '19274:39288'
            name: 标注/
            type: INSTANCE
            layout: layout_4FFGYF
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:39288;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_HDOQB9
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45350'
            name: 标注/
            type: INSTANCE
            layout: layout_BZ7Q9S
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45350;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45352'
            name: 标注/
            type: INSTANCE
            layout: layout_MZQ0GW
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45352;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45324'
            name: 标注/
            type: INSTANCE
            layout: layout_1X35VC
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45324;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45338'
            name: 标注/
            type: INSTANCE
            layout: layout_PBC719
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45338;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45331'
            name: 标注/
            type: INSTANCE
            layout: layout_TLPIPM
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45331;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45339'
            name: 标注/
            type: INSTANCE
            layout: layout_0VM42M
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45339;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45365'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_NDZX24
            componentId: '21821:43423'
            componentProperties:
              - name: 文字#235:0
                value: |-
                  #T13，最多8个字
                  超出容器省略
                type: TEXT
              - name: 方向
                value: ┣┓
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:45365;235:177
                name: Frame 104
                type: FRAME
                layout: layout_S9Q4DE
                borderRadius: 8px
              - id: I17682:45365;235:180
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_RX6JIQ
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
          - id: '17682:45354'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_RGDJC7
            componentId: '21821:43447'
            componentProperties:
              - name: 文字#235:0
                value: size16，stroke1/1.25
                type: TEXT
              - name: 方向
                value: ┗┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:45354;235:219
                name: Frame 104
                type: FRAME
                layout: layout_6C6H8B
                borderRadius: 8px
              - id: I17682:45354;235:222
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_HB3OQP
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
      - id: '17682:45635'
        name: Frame 34551801
        type: FRAME
        layout: layout_0ALFDD
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:45637'
            name: 中按钮
            type: TEXT
            layout: layout_2P9XHW
            text: 中按钮
            textStyle: style_X1ZR25
            fills: fill_QB4BJQ
          - id: '17682:45638'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_IAFNYM
            fills: fill_JL4PA8
            borderRadius: 15px
            componentId: '7451:44854'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45638;17656:30713
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45639'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_517ZBM
            fills: fill_JL4PA8
            borderRadius: 15px
            componentId: '7451:44856'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45639;17656:31058
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45640'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_6XHF95
            fills: fill_JL4PA8
            borderRadius: 15px
            componentId: '7451:44858'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45640;17656:31067
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45641'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_PATM88
            fills: fill_JL4PA8
            borderRadius: 15px
            componentId: '7451:44854'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45641;17656:30713
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45642'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_KMMNKV
            fills: fill_JL4PA8
            borderRadius: 15px
            componentId: '7451:44854'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 右图标#17642:219
                value: 'true'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45642;17656:30713
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:45643'
            name: 标注/
            type: INSTANCE
            layout: layout_K72JGS
            fills: fill_JL4PA8
            componentId: '21821:43761'
            componentProperties:
              - name: Text#748:0
                value: '30'
                type: TEXT
              - name: 方向
                value: ┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45643;212:50
                name: Frame 54
                type: FRAME
                layout: layout_M123ED
              - id: I17682:45643;212:54
                name: Frame 54
                type: FRAME
                layout: layout_YT9D64
          - id: '17682:45644'
            name: 标注/
            type: INSTANCE
            layout: layout_VTOES1
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '10'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45644;212:35
                name: Frame 54
                type: FRAME
                layout: layout_SISDHC
              - id: I17682:45644;212:39
                name: Frame 103
                type: FRAME
                layout: layout_5N9FGL
                borderRadius: 4px
          - id: '17682:45645'
            name: 标注/
            type: INSTANCE
            layout: layout_7YNQIB
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '74'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45645;212:58
                name: Frame 54
                type: FRAME
                layout: layout_JJ0PJV
              - id: I17682:45645;212:62
                name: Frame 103
                type: FRAME
                layout: layout_S921QT
                borderRadius: 4px
          - id: '17682:45646'
            name: 标注/
            type: INSTANCE
            layout: layout_AU3CYW
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '58'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45646;212:58
                name: Frame 54
                type: FRAME
                layout: layout_BTUBC2
              - id: I17682:45646;212:62
                name: Frame 103
                type: FRAME
                layout: layout_9JNOAL
                borderRadius: 4px
          - id: '17682:45647'
            name: 标注/
            type: INSTANCE
            layout: layout_55BRJX
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '96'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45647;212:58
                name: Frame 54
                type: FRAME
                layout: layout_3W89Z2
              - id: I17682:45647;212:62
                name: Frame 103
                type: FRAME
                layout: layout_VWKYZ2
                borderRadius: 4px
          - id: '17682:45648'
            name: 标注/
            type: INSTANCE
            layout: layout_AAX4KD
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '10'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45648;212:35
                name: Frame 54
                type: FRAME
                layout: layout_SISDHC
              - id: I17682:45648;212:39
                name: Frame 103
                type: FRAME
                layout: layout_5N9FGL
                borderRadius: 4px
          - id: '17682:45649'
            name: 标注/
            type: INSTANCE
            layout: layout_6E6EAS
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '2'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45649;212:58
                name: Frame 54
                type: FRAME
                layout: layout_S5G40Y
              - id: I17682:45649;212:62
                name: Frame 103
                type: FRAME
                layout: layout_M8UH7Q
                borderRadius: 4px
          - id: '17682:45650'
            name: 标注/
            type: INSTANCE
            layout: layout_0PEZM8
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45650;212:58
                name: Frame 54
                type: FRAME
                layout: layout_P1PCDE
              - id: I17682:45650;212:62
                name: Frame 103
                type: FRAME
                layout: layout_K6FDKZ
                borderRadius: 4px
          - id: '17682:45651'
            name: 标注/
            type: INSTANCE
            layout: layout_M0YNN3
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45651;212:58
                name: Frame 54
                type: FRAME
                layout: layout_04QCJ7
              - id: I17682:45651;212:62
                name: Frame 103
                type: FRAME
                layout: layout_AZMUEX
                borderRadius: 4px
          - id: '17682:45652'
            name: 标注/
            type: INSTANCE
            layout: layout_3JK4R8
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+2
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45652;212:58
                name: Frame 54
                type: FRAME
                layout: layout_Q9M1FY
              - id: I17682:45652;212:62
                name: Frame 103
                type: FRAME
                layout: layout_BU3MR5
                borderRadius: 4px
          - id: '17682:45653'
            name: 标注/
            type: INSTANCE
            layout: layout_L1WCMH
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+2
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45653;212:58
                name: Frame 54
                type: FRAME
                layout: layout_EVUA3K
              - id: I17682:45653;212:62
                name: Frame 103
                type: FRAME
                layout: layout_KEHL2O
                borderRadius: 4px
          - id: '17682:45654'
            name: 标注/
            type: INSTANCE
            layout: layout_CWAYMS
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45654;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_4VM3ZW
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45655'
            name: 标注/
            type: INSTANCE
            layout: layout_AIESX2
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45655;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_L6W8S0
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45656'
            name: 标注/
            type: INSTANCE
            layout: layout_02YOC7
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45656;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_9WG57A
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45657'
            name: 标注/
            type: INSTANCE
            layout: layout_8FQYOV
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45657;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_HV1BFT
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45658'
            name: 标注/
            type: INSTANCE
            layout: layout_2Q936T
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:45658;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_HV1BFT
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:45659'
            name: 标注/
            type: INSTANCE
            layout: layout_HVCQ5C
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45659;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_KTZ4KV
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45660'
            name: 标注/
            type: INSTANCE
            layout: layout_45L4KG
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45660;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_KTZ4KV
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45661'
            name: 标注/
            type: INSTANCE
            layout: layout_6ICOD2
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45661;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_KTZ4KV
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45662'
            name: 标注/
            type: INSTANCE
            layout: layout_FKA8GH
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45662;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_7B0106
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45663'
            name: 标注/
            type: INSTANCE
            layout: layout_1GWYXT
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45663;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_7B0106
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45664'
            name: 标注/
            type: INSTANCE
            layout: layout_AEQ634
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45664;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45665'
            name: 标注/
            type: INSTANCE
            layout: layout_OUB0XR
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:45665;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:45666'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_DF1O33
            componentId: '21821:43423'
            componentProperties:
              - name: 文字#235:0
                value: |-
                  #T14，最多10个字
                  超出容器省略
                type: TEXT
              - name: 方向
                value: ┣┓
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:45666;235:177
                name: Frame 104
                type: FRAME
                layout: layout_S9Q4DE
                borderRadius: 8px
              - id: I17682:45666;235:180
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_E72GIX
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
          - id: '17682:45636'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_KCHOXT
            componentId: '21821:43447'
            componentProperties:
              - name: 文字#235:0
                value: size16，stroke1/1.25
                type: TEXT
              - name: 方向
                value: ┗┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:45636;235:219
                name: Frame 104
                type: FRAME
                layout: layout_P74GHL
                borderRadius: 8px
              - id: I17682:45636;235:222
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_4MRE1S
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
          - id: '19274:39374'
            name: 标注/
            type: INSTANCE
            layout: layout_B17LTP
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:39374;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_TFN2NN
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '19274:39376'
            name: 标注/
            type: INSTANCE
            layout: layout_X4CDMK
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:39376;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_TFN2NN
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
      - id: '17682:47526'
        name: Frame 34551803
        type: FRAME
        layout: layout_MJAC1O
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:47527'
            name: 文字按钮
            type: TEXT
            layout: layout_3E4KXV
            text: 文字按钮
            textStyle: style_X1ZR25
            fills: fill_QB4BJQ
          - id: '17682:47678'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_H5DUPZ
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 右图标#17671:444
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17671:440
                value: 'false'
                type: BOOLEAN
              - name: 按钮文字#17671:436
                value: 按钮
                type: TEXT
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:47678;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
          - id: '17682:47690'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_FASRNE
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 按钮文字#17671:436
                value: 按钮
                type: TEXT
              - name: 左图标#17671:440
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17671:444
                value: 'false'
                type: BOOLEAN
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:47690;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
          - id: '17682:47697'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_KP29R9
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 按钮文字#17671:436
                value: 按钮
                type: TEXT
              - name: 左图标#17671:440
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17671:444
                value: 'true'
                type: BOOLEAN
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:47697;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
          - id: '17682:47705'
            name: 标注/
            type: INSTANCE
            layout: layout_UZAJXV
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:47705;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_G3EC0V
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:47712'
            name: 标注/
            type: INSTANCE
            layout: layout_MFK4OD
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:47712;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_FPEZS5
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:47718'
            name: 标注/
            type: INSTANCE
            layout: layout_2FDFV7
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:47718;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_FPEZS5
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:47710'
            name: 标注/
            type: INSTANCE
            layout: layout_XT3I3F
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47710;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47713'
            name: 标注/
            type: INSTANCE
            layout: layout_64E6GH
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47713;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47719'
            name: 标注/
            type: INSTANCE
            layout: layout_C97PQZ
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47719;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_BK4Z23
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47716'
            name: 标注/
            type: INSTANCE
            layout: layout_BH3LI3
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47716;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47720'
            name: 标注/
            type: INSTANCE
            layout: layout_201TJC
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47720;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_Y2C6GJ
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47823'
            name: 标注/
            type: INSTANCE
            layout: layout_1O20IE
            fills: fill_JL4PA8
            componentId: '21821:43761'
            componentProperties:
              - name: Text#748:0
                value: '24'
                type: TEXT
              - name: 方向
                value: ┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:47823;212:50
                name: Frame 54
                type: FRAME
                layout: layout_AFC4EX
              - id: I17682:47823;212:54
                name: Frame 54
                type: FRAME
                layout: layout_QXNRKH
          - id: '17682:47824'
            name: 标注/
            type: INSTANCE
            layout: layout_3T2OCQ
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '2'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:47824;212:58
                name: Frame 54
                type: FRAME
                layout: layout_H61HTJ
              - id: I17682:47824;212:62
                name: Frame 103
                type: FRAME
                layout: layout_GJ4QDW
                borderRadius: 4px
          - id: '17682:47825'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_WOX5J5
            componentId: '21821:43391'
            componentProperties:
              - name: 文字#235:0
                value: |-
                  #T13，最多8个字
                  超出容器省略
                type: TEXT
              - name: 方向
                value: ┣
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:47825;235:158
                name: Vector 1
                type: IMAGE-SVG
                layout: layout_SN2NN4
                strokes: stroke_PMRQ5J
              - id: I17682:47825;235:159
                name: Frame 104
                type: FRAME
                layout: layout_6LMT8A
                borderRadius: 8px
          - id: '17682:47843'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_Y1ILV2
            componentId: '21821:43447'
            componentProperties:
              - name: 文字#235:0
                value: size16，stroke1/1.25
                type: TEXT
              - name: 方向
                value: ┗┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:47843;235:219
                name: Frame 104
                type: FRAME
                layout: layout_MU6KQ6
                borderRadius: 8px
              - id: I17682:47843;235:222
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_JX8NVL
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
      - id: '17682:45984'
        name: Frame 34551802
        type: FRAME
        layout: layout_KU2CC5
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:45986'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_4628Q8
            fills: fill_JL4PA8
            borderRadius: 22px
            componentId: '7451:44860'
            componentProperties:
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45986;17656:31319
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
          - id: '17682:45985'
            name: 大按钮
            type: TEXT
            layout: layout_2P9XHW
            text: 大按钮
            textStyle: style_X1ZR25
            fills: fill_QB4BJQ
          - id: '17682:45987'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_3BMR2D
            fills: fill_JL4PA8
            borderRadius: 22px
            componentId: '7451:44862'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45987;17656:31280
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
          - id: '17682:45989'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_QQFADB
            fills: fill_JL4PA8
            borderRadius: 22px
            componentId: '7451:44860'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45989;17656:31319
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
          - id: '17682:45990'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_S5I35F
            fills: fill_JL4PA8
            borderRadius: 22px
            componentId: '7451:44860'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:45990;17656:31319
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
          - id: '17682:45992'
            name: 标注/
            type: INSTANCE
            layout: layout_GNVV6O
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '14'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45992;212:35
                name: Frame 54
                type: FRAME
                layout: layout_T015Z4
              - id: I17682:45992;212:39
                name: Frame 103
                type: FRAME
                layout: layout_9WP808
                borderRadius: 4px
          - id: '17682:45993'
            name: 标注/
            type: INSTANCE
            layout: layout_WU5X2E
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '74'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45993;212:58
                name: Frame 54
                type: FRAME
                layout: layout_HY5WFV
              - id: I17682:45993;212:62
                name: Frame 103
                type: FRAME
                layout: layout_QT4Z5G
                borderRadius: 4px
          - id: '17682:45994'
            name: 标注/
            type: INSTANCE
            layout: layout_VXCBWP
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '280'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45994;212:58
                name: Frame 54
                type: FRAME
                layout: layout_EGFZBH
              - id: I17682:45994;212:62
                name: Frame 103
                type: FRAME
                layout: layout_MNHJ0E
                borderRadius: 4px
          - id: '17682:45996'
            name: 标注/
            type: INSTANCE
            layout: layout_EA1CB9
            fills: fill_JL4PA8
            componentId: '21821:43757'
            componentProperties:
              - name: Text#748:0
                value: '14'
                type: TEXT
              - name: 方向
                value: ┳
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45996;212:35
                name: Frame 54
                type: FRAME
                layout: layout_T015Z4
              - id: I17682:45996;212:39
                name: Frame 103
                type: FRAME
                layout: layout_9WP808
                borderRadius: 4px
          - id: '17682:45997'
            name: 标注/
            type: INSTANCE
            layout: layout_Y893XT
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: '3'
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45997;212:58
                name: Frame 54
                type: FRAME
                layout: layout_DJSU44
              - id: I17682:45997;212:62
                name: Frame 103
                type: FRAME
                layout: layout_UUW711
                borderRadius: 4px
          - id: '17682:45999'
            name: 标注/
            type: INSTANCE
            layout: layout_FPU2Y1
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45999;212:58
                name: Frame 54
                type: FRAME
                layout: layout_M32VRY
              - id: I17682:45999;212:62
                name: Frame 103
                type: FRAME
                layout: layout_L5Y96W
                borderRadius: 4px
          - id: '17682:46000'
            name: 标注/
            type: INSTANCE
            layout: layout_3ZN20U
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+3
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:46000;212:58
                name: Frame 54
                type: FRAME
                layout: layout_CUP3JI
              - id: I17682:46000;212:62
                name: Frame 103
                type: FRAME
                layout: layout_XN06NU
                borderRadius: 4px
          - id: '17682:47512'
            name: 标注/
            type: INSTANCE
            layout: layout_0BKRQ3
            fills: fill_JL4PA8
            componentId: '21821:43763'
            componentProperties:
              - name: Text#748:0
                value: a+3
                type: TEXT
              - name: 方向
                value: ┻
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:47512;212:58
                name: Frame 54
                type: FRAME
                layout: layout_EYS2CH
              - id: I17682:47512;212:62
                name: Frame 103
                type: FRAME
                layout: layout_VR99P2
                borderRadius: 4px
          - id: '17682:46002'
            name: 标注/
            type: INSTANCE
            layout: layout_BCWJBW
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:46002;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MG8TOA
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:46003'
            name: 标注/
            type: INSTANCE
            layout: layout_7ZVV11
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:46003;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_TV3NP5
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:46005'
            name: 标注/
            type: INSTANCE
            layout: layout_R61OT7
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:46005;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MG8TOA
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:47499'
            name: 标注/
            type: INSTANCE
            layout: layout_UFNT9S
            fills: fill_VCSIT4
            componentId: '19278:40596'
            componentProperties:
              - name: 类型
                value: 容器
                type: VARIANT
            children:
              - id: I17682:47499;243:15
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_MG8TOA
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '17682:46007'
            name: 标注/
            type: INSTANCE
            layout: layout_PBM4QA
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:46007;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_AVP6JC
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:46168'
            name: 标注/
            type: INSTANCE
            layout: layout_WC2EJI
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:46168;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_AVP6JC
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:46010'
            name: 标注/
            type: INSTANCE
            layout: layout_XIJJFW
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:46010;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_AVP6JC
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47500'
            name: 标注/
            type: INSTANCE
            layout: layout_N3GC2F
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47500;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_AVP6JC
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:46012'
            name: 标注/
            type: INSTANCE
            layout: layout_S4F2S0
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:46012;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_9G2FIK
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:47501'
            name: 标注/
            type: INSTANCE
            layout: layout_GIOVC0
            fills: fill_WUNCQJ
            strokes: stroke_SVV2ED
            componentId: '19278:40592'
            componentProperties:
              - name: 类型
                value: 占位
                type: VARIANT
            children:
              - id: I17682:47501;243:8
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_9G2FIK
                strokes: stroke_SV7LPE
                opacity: 0.20000000298023224
          - id: '17682:46014'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_BT06P7
            componentId: '21821:43423'
            componentProperties:
              - name: 文字#235:0
                value: |-
                  #T14，最多10个字
                  超出容器省略
                type: TEXT
              - name: 方向
                value: ┣┓
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:46014;235:177
                name: Frame 104
                type: FRAME
                layout: layout_S9Q4DE
                borderRadius: 8px
              - id: I17682:46014;235:180
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_M4ILTT
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
          - id: '17682:46015'
            name: 标注/文字数字指引
            type: INSTANCE
            layout: layout_IEI8TY
            componentId: '21821:43447'
            componentProperties:
              - name: 文字#235:0
                value: size20，stroke1.25
                type: TEXT
              - name: 方向
                value: ┗┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
              - name: 状态
                value: 默认说明
                type: VARIANT
            children:
              - id: I17682:46015;235:219
                name: Frame 104
                type: FRAME
                layout: layout_3RQHLN
                borderRadius: 8px
              - id: I17682:46015;235:222
                name: Vector 2
                type: IMAGE-SVG
                layout: layout_S6DIOJ
                strokes: stroke_PMRQ5J
                borderRadius: 0px 0px 0px 0px
          - id: '17682:45991'
            name: 标注/
            type: INSTANCE
            layout: layout_PD0YH7
            fills: fill_JL4PA8
            componentId: '21821:43759'
            componentProperties:
              - name: Text#748:0
                value: '44'
                type: TEXT
              - name: 方向
                value: ┣
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:45991;212:42
                name: Frame 54
                type: FRAME
                layout: layout_XT6FF2
              - id: I17682:45991;212:46
                name: Frame 54
                type: FRAME
                layout: layout_A5IGQE
          - id: '19274:39419'
            name: 标注/
            type: INSTANCE
            layout: layout_NQD7UA
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:39419;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_WF9G91
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
          - id: '19274:39421'
            name: 标注/
            type: INSTANCE
            layout: layout_2X6S3W
            fills: fill_YL8XEJ
            effects: effect_AWU4OG
            componentId: '19278:40595'
            componentProperties:
              - name: 类型
                value: 间距左右
                type: VARIANT
            children:
              - id: I19274:39421;703:661
                name: Rectangle 453
                type: RECTANGLE
                layout: layout_WF9G91
                strokes: stroke_SV7LPE
                opacity: 0.10000000149011612
      - id: '17656:39515'
        name: 布局
        type: TEXT
        layout: layout_QB7XJ6
        text: 布局
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17682:48323'
        name: 关注、追番和收藏等场景
        type: TEXT
        layout: layout_NJ5FP9
        text: 关注、追番和收藏等场景
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17656:39516'
        name: 按钮在页面位置相对自由，列举一些典型场景
        type: TEXT
        layout: layout_3FVZD1
        text: 按钮在页面位置相对自由，列举一些典型场景
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17682:48324'
        name: 以关注场景为例，介绍类似场景使用规范
        type: TEXT
        layout: layout_5IGTPV
        text: 以关注场景为例，介绍类似场景使用规范
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:39559'
        name: Rectangle 175
        type: RECTANGLE
        layout: layout_QU2KMR
        fills: fill_KPM4B0
      - id: '17656:39560'
        name: 使用场景
        type: TEXT
        layout: layout_83B0V6
        text: 使用场景
        textStyle: style_7ZURI7
        fills: fill_DXAX1L
      - id: '17656:39615'
        name: Rectangle 177
        type: RECTANGLE
        layout: layout_MS1W73
        fills: fill_KPM4B0
      - id: '17656:39616'
        name: 规范用例
        type: TEXT
        layout: layout_X3OLHO
        text: 规范用例
        textStyle: style_7ZURI7
        fills: fill_DXAX1L
      - id: '17656:39618'
        name: 待补充
        type: TEXT
        layout: layout_LV2W39
        text: 待补充
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:39627'
        name: Rectangle 176
        type: RECTANGLE
        layout: layout_R3OAVN
        fills: fill_KPM4B0
      - id: '17656:39628'
        name: Frame 331
        type: FRAME
        layout: layout_L1JZWW
        children:
          - id: '17656:39629'
            name: Frame 328
            type: FRAME
            layout: layout_2P53GZ
            children:
              - id: '17656:39630'
                name: 调用说明
                type: TEXT
                layout: layout_UGJB41
                text: 调用说明
                textStyle: style_7ZURI7
                fills: fill_DXAX1L
      - id: '17656:39631'
        name: Frame 330
        type: FRAME
        layout: layout_WRAXFO
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17656:39632'
            name: image 9
            type: RECTANGLE
            layout: layout_2IC3P2
            fills: fill_BVFREN
            borderRadius: 8px
          - id: '17656:39633'
            name: image 13
            type: RECTANGLE
            layout: layout_XT6APX
            fills: fill_KI4XO9
          - id: '17682:48140'
            name: image 14
            type: RECTANGLE
            layout: layout_D692IX
            fills: fill_3X472Q
          - id: '17656:39634'
            name: Rectangle 1100
            type: RECTANGLE
            layout: layout_SCNF2R
            strokes: stroke_CLRAQU
            borderRadius: 3px
      - id: '17656:39635'
        name: 调用路径
        type: TEXT
        layout: layout_3N2SDO
        text: 调用路径
        textStyle: style_2SPVGB
        fills: fill_DXAX1L
      - id: '17656:39636'
        name: Assets > bilibili UIkit > 02 组件 Components > 01 控件
        type: TEXT
        layout: layout_DBNEP1
        text: Assets > bilibili UIkit > 02 组件 Components > 01 控件
        textStyle: style_61DDWL
        fills: fill_QB4BJQ
      - id: '17656:39637'
        name: 变体设置
        type: TEXT
        layout: layout_Q0OF5T
        text: 变体设置
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17656:39638'
        name: 组件选项对应关系
        type: TEXT
        layout: layout_43SZQQ
        text: 组件选项对应关系
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:39639'
        name: Frame 330
        type: FRAME
        layout: layout_SRUZLD
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:48203'
            name: Group 34551749
            type: GROUP
            layout: layout_DSL5XU
            children:
              - id: '17682:48204'
                name: image 23
                type: RECTANGLE
                layout: layout_U5Q8Q7
                fills: fill_FZ60BP
              - id: '17682:48205'
                name: Rectangle 2214
                type: RECTANGLE
                layout: layout_NTWGXS
                fills: fill_JL4PA8
                effects: effect_ZC07M8
              - id: '17682:48208'
                name: image 30
                type: RECTANGLE
                layout: layout_NHY424
                fills: fill_52DW7S
          - id: '17682:48250'
            name: Group 34551750
            type: GROUP
            layout: layout_8TZ206
            children:
              - id: '17682:48251'
                name: image 23
                type: RECTANGLE
                layout: layout_U5Q8Q7
                fills: fill_FZ60BP
              - id: '17682:48252'
                name: Rectangle 2214
                type: RECTANGLE
                layout: layout_NTWGXS
                fills: fill_JL4PA8
                effects: effect_ZC07M8
              - id: '17682:48255'
                name: image 31
                type: RECTANGLE
                layout: layout_6NATBJ
                fills: fill_XLBC7Z
          - id: '17656:39661'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_UTO68Y
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17656:39661;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48209'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_MJFEQ9
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35236'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48209;17656:29319
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48265'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_ZZEQQF
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35236'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48265;17656:29319
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48276'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_L04DD4
            fills: fill_WXPUWM
            borderRadius: 12px
            componentId: '154:35337'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 黄(付费通知)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48276;17656:29353
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48284'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_TW1C9O
            fills: fill_6JBSFX
            borderRadius: 12px
            componentId: '17682:43027'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 蓝(下载、游戏等)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48284;17682:43028
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48292'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_VD32AV
            fills: fill_JL4PA8
            borderRadius: 12px
            componentId: '7451:44850'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 短
                type: VARIANT
              - name: 颜色
                value: 白
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48292;17656:29375
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48220'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_YZ0578
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48220;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48228'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_D4KOV9
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35249'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48228;17656:30203
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48168'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_B6LX72
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:174'
            componentProperties:
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48168;17656:30312
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48176'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_HCXTWP
            fills: fill_E1IK1A
            borderRadius: 22px
            componentId: '219:246'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 按钮
                type: TEXT
              - name: 尺寸
                value: 大按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48176;17656:31286
                name: 内容
                type: FRAME
                layout: layout_SWL2EO
          - id: '17656:39664'
            name: Group 34551745
            type: GROUP
            layout: layout_3OXYHX
            children:
              - id: '17656:39665'
                name: image 23
                type: RECTANGLE
                layout: layout_U5Q8Q7
                fills: fill_FZ60BP
              - id: '17656:39666'
                name: Rectangle 2214
                type: RECTANGLE
                layout: layout_NTWGXS
                fills: fill_JL4PA8
                effects: effect_ZC07M8
              - id: '17682:48160'
                name: image 29
                type: RECTANGLE
                layout: layout_INKQVT
                fills: fill_113CVU
          - id: '17656:39668'
            name: Vector 474
            type: IMAGE-SVG
            layout: layout_OXOI49
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48210'
            name: Vector 502
            type: IMAGE-SVG
            layout: layout_9N6BC5
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48266'
            name: Vector 505
            type: IMAGE-SVG
            layout: layout_KCGRGA
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17656:39669'
            name: Vector 489
            type: IMAGE-SVG
            layout: layout_KXZR9O
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48211'
            name: Vector 503
            type: IMAGE-SVG
            layout: layout_4ECBOS
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17656:39670'
            name: Vector 490
            type: IMAGE-SVG
            layout: layout_PLXVC3
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48212'
            name: Vector 504
            type: IMAGE-SVG
            layout: layout_XL16I7
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48268'
            name: Vector 507
            type: IMAGE-SVG
            layout: layout_5GVU7Q
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48300'
            name: Vector 508
            type: IMAGE-SVG
            layout: layout_F2DSD8
            strokes: stroke_0689G5
            borderRadius: 4px
          - id: '17682:48301'
            name: Vector 509
            type: IMAGE-SVG
            layout: layout_9L1HBD
            strokes: stroke_0689G5
            borderRadius: 4px
      - id: '17656:39709'
        name: 尺寸
        type: TEXT
        layout: layout_D5HTZK
        text: 尺寸
        textStyle: style_NFRCZI
        fills: fill_DXAX1L
      - id: '17656:39710'
        name: 按钮的尺寸参数
        type: TEXT
        layout: layout_9MQ2V9
        text: 按钮的尺寸参数
        textStyle: style_CF0I3Q
        fills: fill_QB4BJQ
      - id: '17656:40830'
        name: 标题
        type: FRAME
        layout: layout_YP7P8H
        children:
          - id: '17656:40831'
            name: ' '
            type: FRAME
            layout: layout_JW0LMA
            children:
              - id: '17656:40832'
                name: Button
                type: TEXT
                layout: layout_WCO99A
                text: Button
                textStyle: style_B07QB9
                fills: fill_DXAX1L
              - id: '17656:40833'
                name: 按钮
                type: TEXT
                layout: layout_WCO99A
                text: 按钮
                textStyle: style_7MOTCC
                fills: fill_DXAX1L
          - id: '17656:40834'
            name: '-'
            type: TEXT
            layout: layout_AYADJF
            text: '-'
            textStyle: style_F3YXPX
            fills: fill_HEWAVM
          - id: '17656:40835'
            name: Line 7
            type: LINE
            layout: layout_TWINBH
            strokes: stroke_J87A92
      - id: '17682:47849'
        name: Frame 34551804
        type: FRAME
        layout: layout_8SKQET
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:47923'
            name: iPhone 11 Pro / X - 3
            type: FRAME
            layout: layout_VP79WY
            fills: fill_JL4PA8
            borderRadius: 32px
            children:
              - id: '19274:39423'
                name: 标注/
                type: INSTANCE
                layout: layout_7K9WYR
                fills: fill_YL8XEJ
                effects: effect_AWU4OG
                componentId: '19278:40595'
                componentProperties:
                  - name: 类型
                    value: 间距左右
                    type: VARIANT
              - id: '19274:39428'
                name: 标注/
                type: INSTANCE
                layout: layout_J0BR66
                fills: fill_YL8XEJ
                effects: effect_AWU4OG
                componentId: '19278:40595'
                componentProperties:
                  - name: 类型
                    value: 间距左右
                    type: VARIANT
              - id: '19274:39425'
                name: 标注/
                type: INSTANCE
                layout: layout_5UTYY7
                fills: fill_YL8XEJ
                effects: effect_LJSPDN
                componentId: '19278:40594'
                componentProperties:
                  - name: 类型
                    value: 间距上下
                    type: VARIANT
              - id: '17682:48003'
                name: 00 系统/手势条 Home Indicator
                type: INSTANCE
                layout: layout_3ZZM8E
                componentId: '144:1'
                componentProperties:
                  - name: 类型
                    value: 竖屏
                    type: VARIANT
                  - name: 深浅色
                    value: light
                    type: VARIANT
              - id: '17682:48006'
                name: 01 控件/按钮Button
                type: INSTANCE
                layout: layout_6IV5OK
                fills: fill_E1IK1A
                borderRadius: 22px
                componentId: '219:248'
                componentProperties:
                  - name: 文字#25553:0
                    value: 'true'
                    type: BOOLEAN
                  - name: 右图标#17642:219
                    value: 'false'
                    type: BOOLEAN
                  - name: 左图标#17642:2
                    value: 'false'
                    type: BOOLEAN
                  - name: 文字内容#8500:50
                    value: 按钮
                    type: TEXT
                  - name: 尺寸
                    value: 大按钮
                    type: VARIANT
                  - name: 推荐宽度
                    value: 长
                    type: VARIANT
                  - name: 颜色
                    value: 粉(默认)
                    type: VARIANT
                  - name: 样式
                    value: 实色
                    type: VARIANT
                  - name: 状态
                    value: 正常
                    type: VARIANT
              - id: '17682:48013'
                name: 标注/
                type: INSTANCE
                layout: layout_A2DRPD
                fills: fill_JL4PA8
                componentId: '21821:43757'
                componentProperties:
                  - name: Text#748:0
                    value: '16'
                    type: TEXT
                  - name: 方向
                    value: ┳
                    type: VARIANT
                  - name: 背景
                    value: 'off'
                    type: VARIANT
              - id: '17682:48020'
                name: 标注/
                type: INSTANCE
                layout: layout_ADKS54
                fills: fill_JL4PA8
                componentId: '21821:43757'
                componentProperties:
                  - name: Text#748:0
                    value: '16'
                    type: TEXT
                  - name: 方向
                    value: ┳
                    type: VARIANT
                  - name: 背景
                    value: 'off'
                    type: VARIANT
          - id: '17682:48027'
            name: 标注/
            type: INSTANCE
            layout: layout_RINIY0
            fills: fill_JL4PA8
            componentId: '21821:43761'
            componentProperties:
              - name: Text#748:0
                value: '12'
                type: TEXT
              - name: 方向
                value: ┫
                type: VARIANT
              - name: 背景
                value: 'off'
                type: VARIANT
            children:
              - id: I17682:48027;212:50
                name: Frame 54
                type: FRAME
                layout: layout_Q2HOAU
              - id: I17682:48027;212:54
                name: Frame 54
                type: FRAME
                layout: layout_ZG8789
          - id: '17682:48041'
            name: iPhone 11 Pro / X - 4
            type: FRAME
            layout: layout_SUBCDE
            fills: fill_JL4PA8
            borderRadius: 32px
            children:
              - id: '17682:48073'
                name: 04 状态/页面状态Page status
                type: INSTANCE
                layout: layout_DKS4S9
                componentId: '3121:5266'
                componentProperties:
                  - name: 按钮#19864:0
                    value: 'true'
                    type: BOOLEAN
                  - name: 标题#19864:3
                    value: 'false'
                    type: BOOLEAN
                  - name: 双按钮#19864:6
                    value: 'false'
                    type: BOOLEAN
                  - name: 类型
                    value: 默认
                    type: VARIANT
                  - name: 深色模式
                    value: 'false'
                    type: VARIANT
          - id: '17682:48110'
            name: iPhone 11 Pro / X - 5
            type: FRAME
            layout: layout_SX2JT1
            fills: fill_JL4PA8
            borderRadius: 32px
            children:
              - id: '17682:48122'
                name: 提示栏图片
                type: INSTANCE
                layout: layout_FLAZXQ
                fills: fill_JL4PA8
                componentId: '16530:67494'
                componentProperties:
                  - name: 类型
                    value: UP主
                    type: VARIANT
              - id: '17682:48123'
                name: 投稿赢千元奖金，万圣节限量礼品
                type: TEXT
                layout: layout_CNG8L4
                text: 哔哩哔哩弹幕网
                textStyle: style_SOXOLU
                fills: fill_DXAX1L
              - id: '17682:48124'
                name: 万圣节尖叫之夜
                type: TEXT
                layout: layout_TBIA23
                text: 关注UP主，再也不迷路
                textStyle: style_FU349H
                fills: fill_HEWAVM
              - id: '17682:48125'
                name: 01 控件/按钮Button
                type: INSTANCE
                layout: layout_8XLBB7
                fills: fill_E1IK1A
                borderRadius: 12px
                componentId: '154:35175'
                componentProperties:
                  - name: 左图标#17642:2
                    value: 'true'
                    type: BOOLEAN
                  - name: 文字内容#8500:50
                    value: 关注
                    type: TEXT
                  - name: 右图标#17642:219
                    value: 'false'
                    type: BOOLEAN
                  - name: 文字#25553:0
                    value: 'true'
                    type: BOOLEAN
                  - name: 尺寸
                    value: 小按钮
                    type: VARIANT
                  - name: 推荐宽度
                    value: 中
                    type: VARIANT
                  - name: 颜色
                    value: 粉(默认)
                    type: VARIANT
                  - name: 样式
                    value: 实色
                    type: VARIANT
                  - name: 状态
                    value: 正常
                    type: VARIANT
              - id: '17682:48139'
                name: Rectangle 2215
                type: RECTANGLE
                layout: layout_P8RLKO
                fills: fill_KPM4B0
      - id: '17682:48325'
        name: Frame 34551805
        type: FRAME
        layout: layout_X9UU81
        fills: fill_NRUDFR
        borderRadius: 12px
        children:
          - id: '17682:48389'
            name: 正常态
            type: TEXT
            layout: layout_6JOOPV
            text: 正常态
            textStyle: style_61DDWL
            fills: fill_QB4BJQ
          - id: '17682:48390'
            name: 个人空间
            type: TEXT
            layout: layout_3GG2N1
            text: 个人空间
            textStyle: style_9EPBFU
            fills: fill_HEWAVM
          - id: '17682:48391'
            name: 强关注
            type: TEXT
            layout: layout_0UIUV0
            text: 强关注
            textStyle: style_RILPA9
            fills: fill_DXAX1L
          - id: '17682:48392'
            name: 常规
            type: TEXT
            layout: layout_JTPDOJ
            text: 常规
            textStyle: style_RILPA9
            fills: fill_DXAX1L
          - id: '17682:48393'
            name: 弱
            type: TEXT
            layout: layout_TUOT8I
            text: 弱
            textStyle: style_RILPA9
            fills: fill_DXAX1L
          - id: '17682:48394'
            name: 播放页、直播、专栏、搜索
            type: TEXT
            layout: layout_05SWHS
            text: 播放页、直播、专栏、搜索
            textStyle: style_WRURE5
            fills: fill_HEWAVM
          - id: '17682:48395'
            name: 热门、排行榜、推荐关注、动态活动
            type: TEXT
            layout: layout_S45BZ8
            text: 热门、排行榜、推荐关注、动态活动
            textStyle: style_WRURE5
            fills: fill_HEWAVM
          - id: '17682:48396'
            name: 我的关注
            type: TEXT
            layout: layout_R9S24F
            text: 我的关注
            textStyle: style_WRURE5
            fills: fill_HEWAVM
          - id: '17682:48397'
            name: 动态、评论、历史记录
            type: TEXT
            layout: layout_26XVTK
            text: 动态、评论、历史记录
            textStyle: style_WRURE5
            fills: fill_HEWAVM
          - id: '17682:48398'
            name: 粉丝态
            type: TEXT
            layout: layout_EFPK41
            text: 粉丝态
            textStyle: style_61DDWL
            fills: fill_QB4BJQ
          - id: '17682:48399'
            name: 关注态
            type: TEXT
            layout: layout_B02QCJ
            text: 关注态
            textStyle: style_61DDWL
            fills: fill_QB4BJQ
          - id: '17682:48400'
            name: 互粉态
            type: TEXT
            layout: layout_U2DJ6J
            text: 互粉态
            textStyle: style_61DDWL
            fills: fill_QB4BJQ
          - id: '17682:48401'
            name: 特别关注
            type: TEXT
            layout: layout_30KQMM
            text: 特别关注
            textStyle: style_61DDWL
            fills: fill_QB4BJQ
          - id: '17682:48402'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_S06IO1
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:178'
            componentProperties:
              - name: 文字内容#8500:50
                value: 关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48402;17656:31071
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48588'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_RSO4N1
            fills: fill_KPM4B0
            borderRadius: 15px
            componentId: '219:190'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已关注
                type: TEXT
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48588;17656:31069
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48689'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_F95MQR
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48689;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48836'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_RWJEOS
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48836;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48814'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_R85RA8
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48814;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48403'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_DW8HNG
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48403;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48750'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_VAN0PO
            strokes: stroke_O86E6O
            borderRadius: 12px
            componentId: '219:152'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 线框
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48750;17656:29006
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48767'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_QVB5M6
            strokes: stroke_O86E6O
            borderRadius: 12px
            componentId: '219:152'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 线框
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48767;17656:29006
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48405'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_SOASPA
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 按钮文字#17671:436
                value: 关注
                type: TEXT
              - name: 左图标#17671:440
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17671:444
                value: 'false'
                type: BOOLEAN
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48405;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
          - id: '17682:48406'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_AXP5SD
            borderRadius: 2px
            componentId: '303:1666'
            children:
              - id: I17682:48406;303:1667
                name: 按钮文字2
                type: TEXT
                layout: layout_WCO99A
                text: 已关注
                textStyle: style_R2JJVX
                fills: fill_HEWAVM
          - id: '17682:48407'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_SNUPXI
            borderRadius: 2px
            componentId: '303:1666'
            children:
              - id: I17682:48407;303:1667
                name: 按钮文字2
                type: TEXT
                layout: layout_WCO99A
                text: 已互粉
                textStyle: style_R2JJVX
                fills: fill_HEWAVM
          - id: '17682:48409'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_HEMD0E
            fills: fill_E1IK1A
            borderRadius: 15px
            componentId: '219:178'
            componentProperties:
              - name: 文字内容#8500:50
                value: 回关
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48409;17656:31071
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48589'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_LLF0CP
            fills: fill_KPM4B0
            borderRadius: 15px
            componentId: '219:190'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已互粉
                type: TEXT
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48589;17656:31069
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48690'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_M2CCRR
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已互粉
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48690;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48837'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_F1D94X
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已互粉
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48837;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48858'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_AC91ID
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 特别关注
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48858;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48815'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_Y7Y8T5
            fills: fill_KPM4B0
            borderRadius: 12px
            componentId: '154:35375'
            componentProperties:
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 已互粉
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48815;17652:26299
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48604'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_A5Z2CK
            fills: fill_KPM4B0
            borderRadius: 15px
            componentId: '219:190'
            componentProperties:
              - name: 左图标#17642:2
                value: 'true'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 特别关注
                type: TEXT
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 尺寸
                value: 中按钮
                type: VARIANT
              - name: 推荐宽度
                value: 长
                type: VARIANT
              - name: 颜色
                value: 灰
                type: VARIANT
              - name: 样式
                value: 浅底
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48604;17656:31069
                name: 内容
                type: FRAME
                layout: layout_CVAVXY
          - id: '17682:48410'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_8WE6OX
            fills: fill_E1IK1A
            borderRadius: 12px
            componentId: '154:35175'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 回关
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 实色
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48410;17640:19096
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48751'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_0691ST
            strokes: stroke_O86E6O
            borderRadius: 12px
            componentId: '219:152'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 回关
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 线框
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48751;17656:29006
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48768'
            name: 01 控件/按钮Button
            type: INSTANCE
            layout: layout_BP5RLC
            strokes: stroke_O86E6O
            borderRadius: 12px
            componentId: '219:152'
            componentProperties:
              - name: 左图标#17642:2
                value: 'false'
                type: BOOLEAN
              - name: 文字内容#8500:50
                value: 回关
                type: TEXT
              - name: 右图标#17642:219
                value: 'false'
                type: BOOLEAN
              - name: 文字#25553:0
                value: 'true'
                type: BOOLEAN
              - name: 尺寸
                value: 小按钮
                type: VARIANT
              - name: 推荐宽度
                value: 中
                type: VARIANT
              - name: 颜色
                value: 粉(默认)
                type: VARIANT
              - name: 样式
                value: 线框
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48768;17656:29006
                name: 内容
                type: FRAME
                layout: layout_7OIWDX
          - id: '17682:48412'
            name: 01 控件/按钮Button·纯文字
            type: INSTANCE
            layout: layout_RWHVVP
            borderRadius: 2px
            componentId: '303:1662'
            componentProperties:
              - name: 按钮文字#17671:436
                value: 回关
                type: TEXT
              - name: 左图标#17671:440
                value: 'false'
                type: BOOLEAN
              - name: 右图标#17671:444
                value: 'false'
                type: BOOLEAN
              - name: 颜色
                value: 粉（默认）
                type: VARIANT
              - name: 状态
                value: 正常
                type: VARIANT
            children:
              - id: I17682:48412;303:1663
                name: Frame 1
                type: FRAME
                layout: layout_BSHRKQ
          - id: '17682:48424'
            name: Rectangle 167
            type: RECTANGLE
            layout: layout_3TC374
            fills: fill_KPM4B0
          - id: '17682:48425'
            name: Rectangle 168
            type: RECTANGLE
            layout: layout_VRFID2
            fills: fill_KPM4B0
          - id: '17682:48426'
            name: Rectangle 169
            type: RECTANGLE
            layout: layout_HT1F6C
            fills: fill_KPM4B0
globalVars:
  styles:
    layout_08VSAW:
      mode: none
      sizing: {}
      dimensions:
        width: 1200
        height: 5407
    fill_JL4PA8:
      - '#FFFFFF'
    layout_OFAO97:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 280
      dimensions:
        width: 960
        height: 34
    style_7ZURI7:
      fontFamily: PingFang SC
      fontWeight: 500
      fontSize: 22
      lineHeight: 1.5454545454545454em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    fill_DXAX1L:
      - '#18191C'
    layout_0Z2RXU:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 320
      dimensions:
        width: 960
        height: 22
    style_CF0I3Q:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 16
      lineHeight: 1.375em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    fill_QB4BJQ:
      - '#61666D'
    layout_BQN39O:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 354
      dimensions:
        width: 960
        height: 344
    fill_NRUDFR:
      - '#F6F7F8'
    layout_RYZO3O:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 292
        'y': 64
      dimensions:
        width: 375
        height: 456
    layout_QCIXH3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 375
        height: 134
    fill_NKLVEI:
      - '#E7E7E7'
      - type: IMAGE
        imageRef: 6240e4a824b2cd758c2e6cf82fc08263af29d4db
        scaleMode: FILL
        objectFit: cover
        isBackground: false
        imageDownloadArguments:
          needsCropping: false
          requiresImageDimensions: false
    layout_ELCWTN:
      mode: column
      padding: 0px 0px 20px
      sizing:
        horizontal: fixed
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 375
    fill_SXJIAU:
      - type: GRADIENT_LINEAR
        gradientHandlePositions:
          - x: 0.5
            'y': -3.0616171314629196e-17
          - x: 0.5
            'y': 0.9999999999999999
          - x: 0
            'y': 0
        gradientStops:
          - position: 0
            color:
              hex: '#000000'
              opacity: 0.2
          - position: 1
            color:
              hex: '#FFFFFF'
              opacity: 0
    layout_5VPN03:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 12
        'y': 130
      dimensions:
        width: 80
        height: 80
    fill_38XS4S:
      - '#E3E5E7'
      - type: IMAGE
        imageRef: c0e4deabf55af35e8065d60e0cffb23a90b66120
        scaleMode: FILL
        objectFit: cover
        isBackground: false
        imageDownloadArguments:
          needsCropping: false
          requiresImageDimensions: false
    stroke_G75BOA:
      colors:
        - '#FFFFFF'
      strokeWeight: 2px
    layout_HXUZHJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 140
        'y': 142
      dimensions:
        width: 223
        height: 67
    layout_KPFBCS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 12
        'y': 255
      dimensions:
        width: 313
        height: 18
    style_UGRAWK:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 13
      lineHeight: 1.3846153846153846em
      textAlignHorizontal: LEFT
      textAlignVertical: CENTER
    fill_HEWAVM:
      - '#9499A0'
    layout_66D1OI:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 337
        'y': 255
      dimensions:
        width: 26
        height: 18
    fill_Z0YS8Z:
      - '#00699D'
    layout_G9U7GL:
      mode: row
      alignItems: center
      gap: 5px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 12
        'y': 225
    layout_GEVQSY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 738
      dimensions:
        width: 960
        height: 1
    fill_KPM4B0:
      - '#E3E5E7'
    layout_394SWN:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 779
      dimensions:
        width: 960
        height: 34
    layout_6G59DA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 825
      dimensions:
        width: 960
        height: 22
    style_NFRCZI:
      fontFamily: PingFang SC
      fontWeight: 500
      fontSize: 16
      lineHeight: 1.375em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_8OXPMS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 853
      dimensions:
        width: 960
        height: 22
    layout_J806XQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 887
      dimensions:
        width: 960
        height: 225
    layout_BQT8EC:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 396
        'y': 98
      dimensions:
        width: 74
        height: 30
    fill_E1IK1A:
      - '#FF6699'
    layout_CVAVXY:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 2px
      sizing:
        horizontal: fill
        vertical: hug
    layout_WBU8YH:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 490
        'y': 98
      dimensions:
        width: 74
        height: 30
    layout_LDKG31:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 538
        'y': 117
      dimensions:
        width: 10
        height: 36
    layout_SSX4OQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 5
        'y': 0
      dimensions:
        width: 0
        height: 20
    stroke_YCR7TL:
      colors:
        - '#2F3238'
      strokeWeight: 0.30000001192092896px
    layout_1JW1B5:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0.75px 0px 0px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: -3
        'y': 20
      dimensions:
        width: 16
        height: 16
    fill_WB2TH2:
      - '#2F3238'
    layout_OO9MFV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 447
        'y': 123
      dimensions:
        width: 10
        height: 30
    layout_5DBE3T:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 5
        'y': 0
      dimensions:
        width: 0
        height: 14
    layout_93FBVD:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0.75px 0px 0px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: -3
        'y': 14
      dimensions:
        width: 16
        height: 16
    layout_J0V9MW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 427
        'y': 117
      dimensions:
        width: 10
        height: 36
    layout_94CD87:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 367
        'y': 108
      dimensions:
        width: 44
        height: 10
    layout_D82CAB:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 16
        'y': 5
      dimensions:
        width: 28
        height: 0
    layout_B4TRM3:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0.75px 0px 0px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 0
        'y': -3
      dimensions:
        width: 16
        height: 16
    layout_VTAADP:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1124
      dimensions:
        width: 71
        height: 92
    layout_YO5L5B:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 23
        'y': 0
      dimensions:
        width: 48
        height: 17
    style_FU349H:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 12
      lineHeight: 1.4166666666666667em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_UIDU73:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 23
        'y': 25
      dimensions:
        width: 24
        height: 17
    layout_ISYHCR:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 23
        'y': 50
      dimensions:
        width: 48
        height: 17
    layout_389EJD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 23
        'y': 75
      dimensions:
        width: 48
        height: 17
    layout_2AL4CQ:
      mode: row
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 0
    layout_8T2CTJ:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0.75px 0px 0px
      sizing:
        horizontal: fixed
        vertical: fixed
      dimensions:
        width: 16
        height: 16
    layout_4K9IB4:
      mode: row
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 25
    layout_DQ5YVU:
      mode: row
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 50
    layout_IDKKMN:
      mode: row
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 75
    layout_VIZG2V:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1256
      dimensions:
        width: 960
        height: 22
    layout_X7D9VL:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1284
      dimensions:
        width: 960
        height: 22
    layout_70AQB0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2215
      dimensions:
        width: 468
        height: 153
    layout_S5PD4I:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 110
        'y': 48
      dimensions:
        width: 36
        height: 17
    layout_5JH1MD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 322
        'y': 48
      dimensions:
        width: 36
        height: 17
    layout_DSOIHC:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 92
        'y': 81
      dimensions:
        width: 72
        height: 24
    layout_7OIWDX:
      mode: row
      justifyContent: center
      alignItems: center
      alignSelf: stretch
      gap: 2px
      sizing:
        horizontal: fill
        vertical: hug
    layout_I3K4FY:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 304
        'y': 81
      dimensions:
        width: 72
        height: 24
    layout_W8NO4V:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2215
      dimensions:
        width: 468
        height: 20
    style_2SPVGB:
      fontFamily: PingFang SC
      fontWeight: 500
      fontSize: 14
      lineHeight: 1.4285714285714286em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_PR0JWW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2243
      dimensions:
        width: 468
        height: 20
    style_61DDWL:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 14
      lineHeight: 1.4285714285714286em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_6L1TTE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2038
      dimensions:
        width: 468
        height: 153
    layout_I9JEZ3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 90
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_SYZ9YZ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 222
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_F7TGLN:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 354
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_XC5099:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 66
        'y': 81
      dimensions:
        width: 72
        height: 24
    layout_QIUURD:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 198
        'y': 81
      dimensions:
        width: 72
        height: 24
    stroke_O86E6O:
      colors:
        - '#FF6699'
      strokeWeight: 1px
    layout_KTSQOB:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 330
        'y': 81
      dimensions:
        width: 72
        height: 24
    fill_MGY4N5:
      - '#FFECF1'
    layout_VQ4NP5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2038
      dimensions:
        width: 468
        height: 20
    layout_684RBK:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2066
      dimensions:
        width: 294
        height: 20
    layout_TXG6E0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2392
      dimensions:
        width: 468
        height: 153
    layout_K711JM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2392
      dimensions:
        width: 468
        height: 20
    layout_FIV9TW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2420
      dimensions:
        width: 126
        height: 20
    layout_8XNAT3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2448
      dimensions:
        width: 154
        height: 20
    layout_5ZBTFA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2476
      dimensions:
        width: 126
        height: 20
    layout_ARTAYG:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1684
      dimensions:
        width: 468
        height: 153
    layout_K94R0B:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 85
        'y': 48
      dimensions:
        width: 12
        height: 17
    layout_VJEAXM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 209
        'y': 48
      dimensions:
        width: 12
        height: 17
    layout_RVMY55:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 352
        'y': 48
      dimensions:
        width: 12
        height: 17
    layout_RQANR8:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 63
        'y': 81
      dimensions:
        width: 56
        height: 24
    layout_0HQO1L:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 179
        'y': 81
      dimensions:
        width: 72
        height: 24
    layout_44JKAM:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 311
        'y': 81
      dimensions:
        width: 94
        height: 24
    layout_TEFKCE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1684
      dimensions:
        width: 468
        height: 20
    layout_D99C8G:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1712
      dimensions:
        width: 468
        height: 20
    layout_YHD9ZG:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1861
      dimensions:
        width: 468
        height: 20
    layout_HBVTKC:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1861
      dimensions:
        width: 468
        height: 153
    layout_FGOW6H:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 62
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_FB71RQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 382
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_EJTRKJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 142
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_JITIOI:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 302
        'y': 48
      dimensions:
        width: 24
        height: 17
    layout_Q137T7:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 126
        'y': 81
      dimensions:
        width: 56
        height: 24
    fill_WXPUWM:
      - '#FFB027'
    layout_BD6SJK:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 206
        'y': 81
      dimensions:
        width: 56
        height: 24
    fill_6JBSFX:
      - '#00AEEC'
    layout_8Y224S:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 46
        'y': 81
      dimensions:
        width: 56
        height: 24
    layout_YUHSCZ:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 366
        'y': 81
      dimensions:
        width: 56
        height: 24
    layout_2V2E9U:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 286
        'y': 81
      dimensions:
        width: 56
        height: 24
    layout_XLMKQT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1889
      dimensions:
        width: 238
        height: 20
    layout_QNKJMX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1917
      dimensions:
        width: 98
        height: 20
    layout_0PIP3P:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 764
        'y': 1917
      dimensions:
        width: 140
        height: 20
    layout_KC628K:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1945
      dimensions:
        width: 84
        height: 20
    layout_4HXRQF:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 764
        'y': 1945
      dimensions:
        width: 168
        height: 20
    layout_7QA5CE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1973
      dimensions:
        width: 126
        height: 20
    layout_RJME43:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1487
      dimensions:
        width: 468
        height: 173
    layout_42OXAN:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 63
        'y': 48
      dimensions:
        width: 36
        height: 17
    layout_JU9H2L:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 176
        'y': 48
      dimensions:
        width: 36
        height: 17
    layout_NTQQ2F:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 329
        'y': 48
      dimensions:
        width: 36
        height: 17
    layout_H13LTL:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 45
        'y': 91
      dimensions:
        width: 72
        height: 24
    layout_T4W45A:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 157
        'y': 88
      dimensions:
        width: 74
        height: 30
    layout_3D1XUX:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 271
        'y': 81
      dimensions:
        width: 152
        height: 44
    layout_SWL2EO:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 3px
      sizing:
        horizontal: fill
        vertical: hug
    layout_Y5WCVY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1487
      dimensions:
        width: 468
        height: 20
    layout_Y37DH9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1515
      dimensions:
        width: 308
        height: 20
    layout_HSRYCL:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1543
      dimensions:
        width: 224
        height: 20
    layout_KIUKE5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1571
      dimensions:
        width: 183
        height: 20
    layout_M5RUYG:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 1318
      dimensions:
        width: 468
        height: 145
    layout_SSQ3VB:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 142
        'y': 48
      dimensions:
        width: 48
        height: 17
    layout_CFE4EH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 290
        'y': 48
      dimensions:
        width: 48
        height: 17
    layout_8SCGP2:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 130
        'y': 81
      dimensions:
        width: 72
        height: 24
    layout_C7JJDF:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 301
        'y': 81
    layout_BSHRKQ:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 2px
      sizing:
        horizontal: hug
        vertical: hug
    layout_GYHMAL:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1318
      dimensions:
        width: 468
        height: 20
    layout_M40K47:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1346
      dimensions:
        width: 468
        height: 20
    layout_COK5LW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 1374
      dimensions:
        width: 468
        height: 20
    layout_AN1LJ9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 5407
      dimensions:
        width: 80
        height: 3278
    layout_KBSJGI:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 1200
        height: 3278
    fill_F7701Z:
      - '#F1F2F3'
    layout_7IGNB3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2647
      dimensions:
        width: 468
        height: 176
    layout_68J9DN:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 117
        'y': 62
      dimensions:
        width: 72
        height: 24
    layout_U6TROF:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 117
        'y': 96
      dimensions:
        width: 56
        height: 24
    layout_91B0R5:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 117
        'y': 130
      dimensions:
        width: 94
        height: 24
    layout_5ZTGT6:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 213
        'y': 62
      dimensions:
        width: 72
        height: 24
    layout_JNS1WK:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 309
        'y': 62
      dimensions:
        width: 72
        height: 24
    layout_52FX5N:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 87
        'y': 62
      dimensions:
        width: 33
        height: 24
    layout_8GJU1V:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 14
        'y': 0
      dimensions:
        width: 18
        height: 24
    layout_6QOFF5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 5
      dimensions:
        width: 14
        height: 14
    layout_GYDRQR:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2647
      dimensions:
        width: 468
        height: 211
    layout_2P9XHW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 219
        'y': 24
      dimensions:
        width: 30
        height: 14
    style_X1ZR25:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 10
      lineHeight: 1.4em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_HWX6RL:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 117
        'y': 106
      dimensions:
        width: 56
        height: 24
    layout_Z99SAC:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 117
        'y': 150
      dimensions:
        width: 94
        height: 24
    layout_Q5N13Y:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 379
        'y': 62
      dimensions:
        width: 33
        height: 24
    layout_AFC4EX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 1
        'y': 0
      dimensions:
        width: 18
        height: 24
    layout_QXNRKH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 19
        'y': 5
      dimensions:
        width: 14
        height: 14
    layout_UEJDWZ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 117
        'y': 44
      dimensions:
        width: 6
        height: 19
    layout_5A8VRQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 10
      dimensions:
        width: 6
        height: 8
    layout_RRW5SY:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 0
        'y': 0
    layout_N0R02A:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 117
        'y': 85
      dimensions:
        width: 72
        height: 22
    layout_U9NQD8:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 72
        height: 11
    layout_H4I2EV:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 30
        'y': 8
    layout_C6R8AT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 117
        'y': 125
      dimensions:
        width: 56
        height: 22
    layout_OK8YMM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 56
        height: 11
    layout_HQ90QO:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 22
        'y': 8
    layout_DRKPKF:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 117
        'y': 169
      dimensions:
        width: 94
        height: 22
    layout_KIU9RV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 94
        height: 11
    layout_A6ZI8J:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 41
        'y': 8
    layout_A38503:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 183
        'y': 44
      dimensions:
        width: 6
        height: 19
    layout_A5I34N:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 242
        'y': 85
      dimensions:
        width: 2
        height: 19
    layout_H61HTJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 2
        height: 8
    layout_GJ4QDW:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -2
        'y': 5
    layout_AOWIG1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 213
        'y': 85
      dimensions:
        width: 13
        height: 19
    layout_P1PCDE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 13
        height: 8
    layout_K6FDKZ:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 4
        'y': 5
    layout_D9KUYS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 368
        'y': 85
      dimensions:
        width: 13
        height: 18
    layout_B8RYTT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 13
        height: 7
    layout_V15JF0:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 4
        'y': 4
    layout_IY7BSD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 270
        'y': 85
      dimensions:
        width: 15
        height: 19
    layout_Q9M1FY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 15
        height: 8
    layout_BU3MR5:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -1
        'y': 5
    layout_6OEO70:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 309
        'y': 85
      dimensions:
        width: 15
        height: 18
    layout_EVUA3K:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 15
        height: 7
    layout_KEHL2O:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -1
        'y': 4
    layout_JEW9II:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 123
        'y': 65
      dimensions:
        width: 60
        height: 18
    fill_VCSIT4:
      - rgba(255, 255, 255, 0.04)
      - rgba(132, 163, 170, 0.15)
    layout_MRHKKH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 60
        height: 18
    stroke_SV7LPE:
      colors:
        - '#FFFFFF'
      strokeWeight: 0.10000000149011612px
    layout_SX8KOS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 123
        'y': 109
      dimensions:
        width: 44
        height: 18
    layout_CHKAGM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 44
        height: 18
    layout_LZSQ2I:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 123
        'y': 153
      dimensions:
        width: 82
        height: 18
    layout_CVCTV9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 82
        height: 18
    layout_KZ8308:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 219
        'y': 65
      dimensions:
        width: 60
        height: 18
    layout_CZ7F78:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 315
        'y': 65
      dimensions:
        width: 60
        height: 18
    layout_FBJPB7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 140
        'y': 65
      dimensions:
        width: 26
        height: 18
    fill_WUNCQJ:
      - rgba(255, 255, 255, 0.04)
      - rgba(22, 115, 255, 0.06)
    stroke_SVV2ED:
      colors:
        - rgba(0, 0, 0, 0.2)
        - rgba(22, 115, 255, 0.4)
      strokeWeight: 0.5px
    layout_BK4Z23:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 26
        height: 18
    layout_NPMG1V:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 117
        'y': 62
      dimensions:
        width: 6
        height: 24
    fill_YL8XEJ:
      - rgba(255, 255, 255, 0.04)
      - rgba(58, 220, 231, 0.25)
    effect_AWU4OG:
      boxShadow: >-
        inset -0.5px 0px 0px 0px rgba(58, 220, 231, 0.5), inset 0.5px 0px 0px
        0px rgba(58, 220, 231, 0.5), inset -0.5px 0px 0px 0px rgba(0, 0, 0,
        0.1), inset 0.5px 0px 0px 0px rgba(0, 0, 0, 0.1)
    layout_QM4EDW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 6
        height: 24
    layout_JJCEPP:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 183
        'y': 62
      dimensions:
        width: 6
        height: 24
    layout_4FFGYF:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 242
        'y': 62
      dimensions:
        width: 2
        height: 24
    layout_HDOQB9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 2
        height: 24
    layout_BZ7Q9S:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 132
        'y': 109
      dimensions:
        width: 26
        height: 18
    layout_MZQ0GW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 151
        'y': 153
      dimensions:
        width: 26
        height: 18
    layout_1X35VC:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 244
        'y': 65
      dimensions:
        width: 26
        height: 18
    layout_PBC719:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 324
        'y': 65
      dimensions:
        width: 26
        height: 18
    layout_TLPIPM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 226
        'y': 66
      dimensions:
        width: 16
        height: 16
    layout_Y2C6GJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 16
        height: 16
    layout_0VM42M:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 352
        'y': 66
      dimensions:
        width: 16
        height: 16
    layout_NDZX24:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 108
        'y': 35
      dimensions:
        width: 41
        height: 30
    layout_S9Q4DE:
      mode: column
      justifyContent: center
      alignItems: flex-end
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: -16
        'y': 0
      dimensions:
        width: 16
        height: 16
    layout_RX6JIQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 8
      dimensions:
        width: 41
        height: 22
    stroke_PMRQ5J:
      colors:
        - '#4C6DE4'
      strokeWeight: 0.5px
    layout_RGDJC7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 234
        'y': 83
      dimensions:
        width: 35
        height: 47
    layout_6C6H8B:
      mode: column
      justifyContent: center
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 35
        'y': 31
      dimensions:
        width: 16
        height: 16
    layout_HB3OQP:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 35
        height: 39
    layout_0ALFDD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2647
      dimensions:
        width: 468
        height: 211
    layout_IAFNYM:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 116
        'y': 62
      dimensions:
        width: 74
        height: 30
    layout_517ZBM:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 116
        'y': 106
      dimensions:
        width: 58
        height: 30
    layout_6XHF95:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 116
        'y': 150
      dimensions:
        width: 96
        height: 30
    layout_PATM88:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 212
        'y': 62
      dimensions:
        width: 74
        height: 30
    layout_KMMNKV:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 308
        'y': 62
      dimensions:
        width: 74
        height: 30
    layout_K72JGS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 379
        'y': 62
      dimensions:
        width: 33
        height: 30
    layout_M123ED:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 1
        'y': 0
      dimensions:
        width: 18
        height: 30
    layout_YT9D64:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 19
        'y': 8
      dimensions:
        width: 14
        height: 14
    layout_VTOES1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 116
        'y': 40
      dimensions:
        width: 10
        height: 23
    layout_SISDHC:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 10
      dimensions:
        width: 10
        height: 12
    layout_5N9FGL:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -0.5
        'y': 0
    layout_7YNQIB:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 116
        'y': 90
      dimensions:
        width: 74
        height: 18
    layout_JJ0PJV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 74
        height: 7
    layout_S921QT:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 31
        'y': 4
    layout_AU3CYW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 116
        'y': 129
      dimensions:
        width: 58
        height: 23
    layout_BTUBC2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 58
        height: 12
    layout_9JNOAL:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 23
        'y': 9
    layout_55BRJX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 116
        'y': 173
      dimensions:
        width: 96
        height: 25
    layout_3W89Z2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 96
        height: 14
    layout_VWKYZ2:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 42
        'y': 11
    layout_AAX4KD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 180
        'y': 40
      dimensions:
        width: 10
        height: 23
    layout_6E6EAS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 241
        'y': 83
      dimensions:
        width: 2
        height: 22
    layout_S5G40Y:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 2
        height: 11
    layout_M8UH7Q:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -2
        'y': 8
    layout_0PEZM8:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 212
        'y': 89
      dimensions:
        width: 13
        height: 19
    layout_M0YNN3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 369
        'y': 89
      dimensions:
        width: 12
        height: 18
    layout_04QCJ7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 12
        height: 7
    layout_AZMUEX:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 3
        'y': 4
    layout_3JK4R8:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 271
        'y': 88
      dimensions:
        width: 15
        height: 19
    layout_L1WCMH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 308
        'y': 89
      dimensions:
        width: 15
        height: 18
    layout_CWAYMS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 126
        'y': 67
      dimensions:
        width: 54
        height: 20
    layout_4VM3ZW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 54
        height: 20
    layout_AIESX2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 126
        'y': 111
      dimensions:
        width: 38
        height: 20
    layout_L6W8S0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 38
        height: 20
    layout_02YOC7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 126
        'y': 155
      dimensions:
        width: 76
        height: 20
    layout_9WG57A:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 76
        height: 20
    layout_8FQYOV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 222
        'y': 68
      dimensions:
        width: 54
        height: 18
    layout_HV1BFT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 54
        height: 18
    layout_2Q936T:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 318
        'y': 68
      dimensions:
        width: 54
        height: 18
    layout_HVCQ5C:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 139
        'y': 67
      dimensions:
        width: 28
        height: 20
    layout_KTZ4KV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 28
        height: 20
    layout_45L4KG:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 131
        'y': 111
      dimensions:
        width: 28
        height: 20
    layout_6ICOD2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 150
        'y': 155
      dimensions:
        width: 28
        height: 20
    layout_FKA8GH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 243
        'y': 68
      dimensions:
        width: 28
        height: 18
    layout_7B0106:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 28
        height: 18
    layout_1GWYXT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 323
        'y': 68
      dimensions:
        width: 28
        height: 18
    layout_AEQ634:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 225
        'y': 69
      dimensions:
        width: 16
        height: 16
    layout_OUB0XR:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 353
        'y': 69
      dimensions:
        width: 16
        height: 16
    layout_DF1O33:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 105
        'y': 31
      dimensions:
        width: 42
        height: 36
    layout_E72GIX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 8
      dimensions:
        width: 42
        height: 28
    layout_KCHOXT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 233
        'y': 85
      dimensions:
        width: 39
        height: 46
    layout_P74GHL:
      mode: column
      justifyContent: center
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 39
        'y': 30
      dimensions:
        width: 16
        height: 16
    layout_4MRE1S:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 39
        height: 38
    layout_B17LTP:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 116
        'y': 62
      dimensions:
        width: 10
        height: 30
    layout_TFN2NN:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 10
        height: 30
    layout_X4CDMK:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 180
        'y': 62
      dimensions:
        width: 10
        height: 30
    layout_MJAC1O:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 2882
      dimensions:
        width: 468
        height: 211
    layout_3E4KXV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 214
        'y': 24
      dimensions:
        width: 40
        height: 14
    layout_H5DUPZ:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 159
        'y': 98
    layout_FASRNE:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 205
        'y': 98
    layout_KP29R9:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 269
        'y': 98
    layout_UZAJXV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 159
        'y': 98
      dimensions:
        width: 26
        height: 24
    layout_G3EC0V:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 26
        height: 24
    layout_MFK4OD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 205
        'y': 98
      dimensions:
        width: 44
        height: 24
    layout_FPEZS5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 44
        height: 24
    layout_2FDFV7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 269
        'y': 98
      dimensions:
        width: 44
        height: 24
    layout_XT3I3F:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 159
        'y': 101
      dimensions:
        width: 26
        height: 18
    layout_64E6GH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 223
        'y': 101
      dimensions:
        width: 26
        height: 18
    layout_C97PQZ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 269
        'y': 101
      dimensions:
        width: 26
        height: 18
    layout_BH3LI3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 205
        'y': 102
      dimensions:
        width: 16
        height: 16
    layout_201TJC:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 297
        'y': 102
      dimensions:
        width: 16
        height: 16
    layout_1O20IE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 315
        'y': 98
      dimensions:
        width: 33
        height: 24
    layout_3T2OCQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 221
        'y': 119
      dimensions:
        width: 2
        height: 19
    layout_WOX5J5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 127
        'y': 102
      dimensions:
        width: 32
        height: 16
    layout_SN2NN4:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 2
        'y': 8
      dimensions:
        width: 30
        height: 0
    layout_6LMT8A:
      mode: column
      justifyContent: center
      alignItems: flex-end
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: -14
        'y': 0
      dimensions:
        width: 16
        height: 16
    layout_Y1ILV2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 213
        'y': 118
      dimensions:
        width: 35
        height: 50
    layout_MU6KQ6:
      mode: column
      justifyContent: center
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 35
        'y': 34
      dimensions:
        width: 16
        height: 16
    layout_JX8NVL:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 35
        height: 42
    layout_KU2CC5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2882
      dimensions:
        width: 468
        height: 211
    layout_4628Q8:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 40
        'y': 62
      dimensions:
        width: 152
        height: 44
    layout_3BMR2D:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 40
        'y': 136
      dimensions:
        width: 280
        height: 44
    layout_QQFADB:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 204
        'y': 62
      dimensions:
        width: 152
        height: 44
    layout_S5I35F:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 368
        'y': 62
      dimensions:
        width: 152
        height: 44
    layout_GNVV6O:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 44
      dimensions:
        width: 14
        height: 19
    layout_T015Z4:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 10
      dimensions:
        width: 14
        height: 8
    layout_9WP808:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 1.5
        'y': 0
    layout_WU5X2E:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 108
      dimensions:
        width: 152
        height: 23
    layout_HY5WFV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 152
        height: 12
    layout_QT4Z5G:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 70
        'y': 9
    layout_VXCBWP:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 173
      dimensions:
        width: 280
        height: 30
    layout_EGFZBH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 280
        height: 19
    layout_MNHJ0E:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 131
        'y': 16
    layout_EA1CB9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 178
        'y': 44
      dimensions:
        width: 14
        height: 19
    layout_Y893XT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 271
        'y': 91
      dimensions:
        width: 3
        height: 29
    layout_DJSU44:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 3
        height: 18
    layout_UUW711:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: -1
        'y': 15
    layout_FPU2Y1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 306
        'y': 92
      dimensions:
        width: 50
        height: 29
    layout_M32VRY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 50
        height: 18
    layout_L5Y96W:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 22
        'y': 15
    layout_3ZN20U:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 204
        'y': 98
      dimensions:
        width: 47
        height: 23
    layout_CUP3JI:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 47
        height: 12
    layout_XN06NU:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 15
        'y': 9
    layout_0BKRQ3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 368
        'y': 98
      dimensions:
        width: 50
        height: 23
    layout_EYS2CH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 1
      dimensions:
        width: 50
        height: 12
    layout_VR99P2:
      mode: row
      justifyContent: center
      alignItems: center
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 16
        'y': 9
    layout_BCWJBW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 54
        'y': 73
      dimensions:
        width: 124
        height: 22
    layout_MG8TOA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 124
        height: 22
    layout_7ZVV11:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 54
        'y': 147
      dimensions:
        width: 252
        height: 22
    layout_TV3NP5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 252
        height: 22
    layout_R61OT7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 218
        'y': 73
      dimensions:
        width: 124
        height: 22
    layout_UFNT9S:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 382
        'y': 73
      dimensions:
        width: 124
        height: 22
    layout_PBM4QA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 100
        'y': 73
      dimensions:
        width: 32
        height: 22
    layout_AVP6JC:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 32
        height: 22
    layout_WC2EJI:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 164
        'y': 147
      dimensions:
        width: 32
        height: 22
    layout_XIJJFW:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 274
        'y': 73
      dimensions:
        width: 32
        height: 22
    layout_N3GC2F:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 418
        'y': 73
      dimensions:
        width: 32
        height: 22
    layout_S4F2S0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 251
        'y': 74
      dimensions:
        width: 20
        height: 20
    layout_9G2FIK:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 20
        height: 20
    layout_GIOVC0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 453
        'y': 74
      dimensions:
        width: 20
        height: 20
    layout_BT06P7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 103
        'y': 21
      dimensions:
        width: 21
        height: 52
    layout_M4ILTT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 8
      dimensions:
        width: 21
        height: 44
    layout_IEI8TY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 261
        'y': 94
      dimensions:
        width: 39
        height: 42
    layout_3RQHLN:
      mode: column
      justifyContent: center
      gap: 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 39
        'y': 26
      dimensions:
        width: 16
        height: 16
    layout_S6DIOJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 39
        height: 34
    layout_PD0YH7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 11
        'y': 62
      dimensions:
        width: 31
        height: 44
    layout_XT6FF2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 14
        'y': 0
      dimensions:
        width: 16
        height: 44
    layout_A5IGQE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 15
      dimensions:
        width: 14
        height: 14
    layout_NQD7UA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 62
      dimensions:
        width: 14
        height: 44
    layout_WF9G91:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 14
        height: 44
    layout_2X6S3W:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 178
        'y': 62
      dimensions:
        width: 14
        height: 44
    layout_QB7XJ6:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3133
      dimensions:
        width: 960
        height: 22
    layout_NJ5FP9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3758
      dimensions:
        width: 960
        height: 22
    layout_3FVZD1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3161
      dimensions:
        width: 960
        height: 22
    layout_5IGTPV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3786
      dimensions:
        width: 960
        height: 22
    layout_QU2KMR:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3659
      dimensions:
        width: 960
        height: 1
    layout_83B0V6:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3700
      dimensions:
        width: 960
        height: 34
    layout_MS1W73:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4243
      dimensions:
        width: 960
        height: 1
    layout_X3OLHO:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4284
      dimensions:
        width: 960
        height: 34
    layout_LV2W39:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4324
      dimensions:
        width: 960
        height: 22
    layout_R3OAVN:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4386
      dimensions:
        width: 960
        height: 1
    layout_L1JZWW:
      mode: column
      gap: 12px
      sizing:
        horizontal: fixed
        vertical: hug
      locationRelativeToParent:
        x: 120
        'y': 4427
      dimensions:
        width: 960
    layout_2P53GZ:
      mode: column
      alignSelf: stretch
      gap: 12px
      sizing:
        horizontal: fill
        vertical: hug
    layout_UGJB41:
      mode: none
      sizing:
        horizontal: fill
        vertical: hug
    layout_WRAXFO:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4485
      dimensions:
        width: 468
        height: 388
    layout_2IC3P2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 100
        'y': 40
      dimensions:
        width: 404
        height: 652
    fill_BVFREN:
      - type: IMAGE
        imageRef: 346c1011d26c3c45654cb6d249ffda03f2e5052e
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.9972222447395325
              - 0
              - 0.0027777778450399637
            - - 0
              - 0.9986168146133423
              - 0.000691598339471966
          filenameSuffix: 6c2499
    layout_XT6APX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 100
        'y': 125
      dimensions:
        width: 160
        height: 224
    fill_KI4XO9:
      - type: IMAGE
        imageRef: 3c9316710262413fbff453974d10665f27b2c514
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.5414918661117554
              - 0
              - 0.0008121747523546219
            - - 0
              - 0.30026811361312866
              - 0.11930295079946518
          filenameSuffix: 165a02
    layout_D692IX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 100
        'y': 351
      dimensions:
        width: 160
        height: 139
    fill_3X472Q:
      - type: IMAGE
        imageRef: 3c9316710262413fbff453974d10665f27b2c514
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.5414918661117554
              - 0
              - 0.0008121747523546219
            - - 0
              - 0.18632709980010986
              - 0.45174267888069153
          filenameSuffix: 433a73
    layout_SCNF2R:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 106
        'y': 328
      dimensions:
        width: 149
        height: 46
    stroke_CLRAQU:
      colors:
        - '#1E7FD9'
      strokeWeight: 1px
    layout_3N2SDO:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 4485
      dimensions:
        width: 468
        height: 20
    layout_DBNEP1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 612
        'y': 4513
      dimensions:
        width: 468
        height: 20
    layout_Q0OF5T:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4913
      dimensions:
        width: 960
        height: 22
    layout_43SZQQ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4941
      dimensions:
        width: 960
        height: 22
    layout_SRUZLD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 4975
      dimensions:
        width: 960
        height: 328
    layout_DSL5XU:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 311
        'y': 40
      dimensions:
        width: 189
        height: 330
    layout_U5Q8Q7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 189
        height: 252
    fill_FZ60BP:
      - type: IMAGE
        imageRef: 89178b9142aa6f26aa15c50e0969c5e9ac8ba08b
        scaleMode: FILL
        objectFit: cover
        isBackground: false
        imageDownloadArguments:
          needsCropping: false
          requiresImageDimensions: false
    layout_NTWGXS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 85.93
        'y': 152
      dimensions:
        width: 103
        height: 178
    effect_ZC07M8:
      boxShadow: inset 0.4300000071525574px 0px 0px 0px rgba(230, 230, 230, 1)
    layout_NHY424:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 88
        'y': 146
      dimensions:
        width: 98
        height: 174
    fill_52DW7S:
      - type: IMAGE
        imageRef: 35ad78855cb4a289caacc07a5b45f4500f4a4d28
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.8177782893180847
              - 0
              - 0.14727036654949188
            - - 0
              - 0.9508196711540222
              - 0.049180325120687485
          filenameSuffix: 469b61
    layout_8TZ206:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 634
        'y': 40
      dimensions:
        width: 189
        height: 330
    layout_6NATBJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 88
        'y': 146
      dimensions:
        width: 97
        height: 167
    fill_XLBC7Z:
      - type: IMAGE
        imageRef: 405918d171e4d4d4a7cf53ccadea97235e76676a
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.843478262424469
              - 0
              - 0.125216543674469
            - - 0
              - 0.9402565360069275
              - 0.05802314728498459
          filenameSuffix: 6c287f
    layout_UTO68Y:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 200
        'y': 187
      dimensions:
        width: 72
        height: 24
    layout_MJFEQ9:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 202
      dimensions:
        width: 56
        height: 24
    layout_ZZEQQF:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 842
        'y': 200
      dimensions:
        width: 56
        height: 24
    layout_L04DD4:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 842
        'y': 230
      dimensions:
        width: 56
        height: 24
    layout_TW1C9O:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 842
        'y': 260
      dimensions:
        width: 56
        height: 24
    layout_VD32AV:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 842
        'y': 290
      dimensions:
        width: 56
        height: 24
    layout_YZ0578:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 236
      dimensions:
        width: 72
        height: 24
    layout_D4KOV9:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 270
      dimensions:
        width: 94
        height: 24
    layout_B6LX72:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 200
        'y': 219
      dimensions:
        width: 74
        height: 30
    layout_HCXTWP:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 200
        'y': 257
      dimensions:
        width: 152
        height: 44
    layout_3OXYHX:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: -8
        'y': 40
      dimensions:
        width: 189
        height: 330
    layout_INKQVT:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 87.2
        'y': 120.4
      dimensions:
        width: 97.8
        height: 202.83
    fill_113CVU:
      - type: IMAGE
        imageRef: cfac2c2957ba5509e195a73c491ebc24a1b4709a
        scaleMode: STRETCH
        objectFit: fill
        isBackground: false
        imageDownloadArguments:
          needsCropping: true
          requiresImageDimensions: false
          cropTransform:
            - - 0.8805907368659973
              - 0
              - 0.08131443709135056
            - - 0
              - 1
              - -7.105427357601002e-15
          filenameSuffix: 17993d
    layout_OXOI49:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 170
        'y': 198
      dimensions:
        width: 30
        height: 24
    stroke_0689G5:
      colors:
        - '#000000'
      strokeWeight: 0.5px
    layout_9N6BC5:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 491
        'y': 213
      dimensions:
        width: 30
        height: 24
    layout_KCGRGA:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 812
        'y': 211
      dimensions:
        width: 30
        height: 38
    layout_KXZR9O:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 170
        'y': 233
      dimensions:
        width: 30
        height: 0
    layout_4ECBOS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 491
        'y': 248
      dimensions:
        width: 30
        height: 0
    layout_PLXVC3:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 170
        'y': 243
      dimensions:
        width: 30
        height: 38
    layout_XL16I7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 491
        'y': 258
      dimensions:
        width: 30
        height: 23
    layout_5GVU7Q:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 812
        'y': 241
      dimensions:
        width: 30
        height: 19
    layout_F2DSD8:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 812
        'y': 270
      dimensions:
        width: 30
        height: 5
    layout_9L1HBD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 812
        'y': 279
      dimensions:
        width: 30
        height: 25
    layout_D5HTZK:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2585
      dimensions:
        width: 960
        height: 22
    layout_9MQ2V9:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 2613
      dimensions:
        width: 960
        height: 22
    layout_YP7P8H:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 120
      dimensions:
        width: 960
        height: 120
    layout_JW0LMA:
      mode: row
      alignItems: flex-end
      gap: 16px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 280.26
        height: 72
    layout_WCO99A:
      mode: none
      sizing:
        horizontal: hug
        vertical: hug
    style_B07QB9:
      fontFamily: SF Pro Display
      fontWeight: 600
      fontSize: 60
      lineHeight: 1.193359375em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    style_7MOTCC:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 32
      lineHeight: 1.875em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_AYADJF:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 78
      dimensions:
        width: 960
        height: 22
    style_F3YXPX:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 16
      lineHeight: 1.399999976158142em
      textAlignHorizontal: LEFT
      textAlignVertical: TOP
    layout_TWINBH:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 120
      dimensions:
        width: 960
        height: 0
    stroke_J87A92:
      colors:
        - '#E3E5E7'
      strokeWeight: 0.5px
    layout_8SKQET:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3195
      dimensions:
        width: 960
        height: 424
    layout_VP79WY:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 80
        'y': -324
      dimensions:
        width: 375
        height: 456
    layout_7K9WYR:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 366
      dimensions:
        width: 16
        height: 56
    layout_J0BR66:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 359
        'y': 366
      dimensions:
        width: 16
        height: 56
    layout_5UTYY7:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 410
      dimensions:
        width: 375
        height: 12
    effect_LJSPDN:
      boxShadow: >-
        inset 0px -0.5px 0px 0px rgba(58, 220, 231, 0.5), inset 0px 0.5px 0px
        0px rgba(58, 220, 231, 0.5), inset 0px -0.5px 0px 0px rgba(0, 0, 0,
        0.1), inset 0px 0.5px 0px 0px rgba(0, 0, 0, 0.1)
    layout_3ZZM8E:
      mode: column
      justifyContent: flex-end
      alignItems: center
      gap: 10px
      padding: 21px 0px 8px
      sizing:
        horizontal: fixed
        vertical: hug
      locationRelativeToParent:
        x: 121
        'y': 422
      dimensions:
        width: 134
    layout_6IV5OK:
      mode: row
      justifyContent: center
      alignItems: center
      padding: 0px 14px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 16
        'y': 366
      dimensions:
        width: 343
        height: 44
    layout_A2DRPD:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 343
      dimensions:
        width: 16
        height: 19
    layout_ADKS54:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 359
        'y': 343
      dimensions:
        width: 16
        height: 19
    layout_RINIY0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 455
        'y': 86
      dimensions:
        width: 22
        height: 12
    layout_Q2HOAU:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 1
        'y': 0
      dimensions:
        width: 7
        height: 12
    layout_ZG8789:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 8
        'y': -1
      dimensions:
        width: 14
        height: 14
    layout_SUBCDE:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 505
        'y': -45
      dimensions:
        width: 375
        height: 513
    layout_DKS4S9:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 6px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 47
        'y': 125
    layout_SX2JT1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 80
        'y': 172
      dimensions:
        width: 375
        height: 513
    layout_FLAZXQ:
      mode: row
      gap: 10px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 12
        'y': 192
    layout_CNG8L4:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 64
        'y': 192.5
      dimensions:
        width: 173
        height: 20
    style_SOXOLU:
      fontFamily: PingFang SC
      fontWeight: 500
      fontSize: 14
      lineHeight: 1.4285714285714286em
      textAlignHorizontal: LEFT
      textAlignVertical: CENTER
    layout_TBIA23:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 64
        'y': 214.5
      dimensions:
        width: 173
        height: 17
    layout_8XLBB7:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 291
        'y': 200
      dimensions:
        width: 72
        height: 24
    layout_P8RLKO:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 375
        height: 176
    layout_X9UU81:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 120
        'y': 3820
      dimensions:
        width: 960
        height: 383
    layout_6JOOPV:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 127
      dimensions:
        width: 42
        height: 20
    layout_3GG2N1:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 160
        'y': 83
      dimensions:
        width: 120
        height: 20
    style_9EPBFU:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 12
      lineHeight: 1.6666666666666667em
      textAlignHorizontal: CENTER
      textAlignVertical: TOP
    layout_0UIUV0:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 160
        'y': 40
      dimensions:
        width: 120
        height: 20
    style_RILPA9:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 14
      lineHeight: 1.4285714285714286em
      textAlignHorizontal: CENTER
      textAlignVertical: TOP
    layout_JTPDOJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 344
        'y': 40
      dimensions:
        width: 120
        height: 20
    layout_TUOT8I:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 504
        'y': 40
      dimensions:
        width: 329
        height: 20
    layout_05SWHS:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 344
        'y': 76
      dimensions:
        width: 120
        height: 34
    style_WRURE5:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 12
      lineHeight: 1.3999999364217122em
      textAlignHorizontal: CENTER
      textAlignVertical: TOP
    layout_S45BZ8:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 504
        'y': 76
      dimensions:
        width: 120
        height: 34
    layout_R9S24F:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 636
        'y': 84
      dimensions:
        width: 120
        height: 17
    layout_26XVTK:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 768
        'y': 76
      dimensions:
        width: 120
        height: 17
    layout_EFPK41:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 177
      dimensions:
        width: 42
        height: 20
    layout_B02QCJ:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 227
      dimensions:
        width: 42
        height: 20
    layout_U2DJ6J:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 277
      dimensions:
        width: 42
        height: 20
    layout_30KQMM:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 40
        'y': 327
      dimensions:
        width: 56
        height: 20
    layout_S06IO1:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 136
        'y': 122
      dimensions:
        width: 168
        height: 30
    layout_RSO4N1:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 136
        'y': 222
      dimensions:
        width: 168
        height: 30
    layout_F95MQR:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 371
        'y': 225
      dimensions:
        width: 72
        height: 24
    layout_RWJEOS:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 657
        'y': 225
      dimensions:
        width: 80
        height: 24
    layout_R85RA8:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 225
      dimensions:
        width: 72
        height: 24
    layout_DW8HNG:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 371
        'y': 125
      dimensions:
        width: 72
        height: 24
    layout_VAN0PO:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 125
      dimensions:
        width: 72
        height: 24
    layout_QVB5M6:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 657
        'y': 125
      dimensions:
        width: 80
        height: 24
    layout_SOASPA:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 806
        'y': 125
    layout_AXP5SD:
      mode: row
      gap: 10px
      padding: 3px 2px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 806
        'y': 225
    style_R2JJVX:
      fontFamily: PingFang SC
      fontWeight: 400
      fontSize: 13
      lineHeight: 1.3846153846153846em
      textAlignHorizontal: CENTER
      textAlignVertical: CENTER
    layout_SNUPXI:
      mode: row
      gap: 10px
      padding: 3px 2px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 806
        'y': 275
    layout_HEMD0E:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 136
        'y': 172
      dimensions:
        width: 168
        height: 30
    layout_LLF0CP:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 136
        'y': 272
      dimensions:
        width: 168
        height: 30
    layout_M2CCRR:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 371
        'y': 275
      dimensions:
        width: 72
        height: 24
    layout_F1D94X:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 657
        'y': 275
      dimensions:
        width: 80
        height: 24
    layout_AC91ID:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 657
        'y': 325
      dimensions:
        width: 80
        height: 24
    layout_Y7Y8T5:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 275
      dimensions:
        width: 72
        height: 24
    layout_A5Z2CK:
      mode: row
      justifyContent: stretch
      alignItems: stretch
      gap: 10px
      padding: 0px 10px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 136
        'y': 322
      dimensions:
        width: 168
        height: 30
    layout_8WE6OX:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 371
        'y': 175
      dimensions:
        width: 72
        height: 24
    layout_0691ST:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 521
        'y': 175
      dimensions:
        width: 72
        height: 24
    layout_BP5RLC:
      mode: column
      justifyContent: center
      alignItems: center
      gap: 10px
      padding: 3px 6px
      sizing:
        horizontal: fixed
        vertical: fixed
      locationRelativeToParent:
        x: 657
        'y': 175
      dimensions:
        width: 80
        height: 24
    layout_RWHVVP:
      mode: column
      gap: 10px
      padding: 2.5px 0px
      sizing:
        horizontal: hug
        vertical: hug
      locationRelativeToParent:
        x: 815
        'y': 175
    layout_3TC374:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 136
        'y': 64
      dimensions:
        width: 168
        height: 1
    layout_VRFID2:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 344
        'y': 64
      dimensions:
        width: 120
        height: 1
    layout_HT1F6C:
      mode: none
      sizing: {}
      locationRelativeToParent:
        x: 504
        'y': 64
      dimensions:
        width: 384
        height: 1
