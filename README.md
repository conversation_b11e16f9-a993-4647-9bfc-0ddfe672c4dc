# Figma Token Optimizer

一个轻量级的Figma Token优化工具，通过智能关键词过滤来最小化数据传输量，专为LLM集成设计。

## 核心理念

基于一个简单而有效的思路：
1. 从figma.yml的name字段提取所有关键词
2. 将关键词提供给LLM让其选择相关的关键词
3. 根据选中的关键词过滤数据，只保留匹配的组件和节点
4. 实现50-90%的数据量减少，大幅提升传输效率

## 功能特性

- 🎯 **关键词提取**: 自动从组件名称中提取所有关键词
- 🤖 **LLM友好**: 专为LLM集成设计的简单API
- 🔍 **智能过滤**: 基于关键词的高效数据过滤
- 📉 **数据压缩**: 典型情况下可减少50-90%的数据量
- ⚡ **轻量级**: 无需复杂的索引或缓存机制
- 🔌 **即插即用**: 简单的一键式解决方案

## 项目结构

```
figma-token-optimizer/
├── src/
│   ├── simple/         # 简化版核心模块
│   │   ├── KeywordExtractor.js    # 关键词提取器
│   │   ├── KeywordFilter.js       # 关键词过滤器
│   │   ├── FigmaTokenOptimizer.js # 主优化器
│   │   └── index.js               # 简化API入口
│   └── core/           # 完整版功能模块（可选）
├── examples/           # 使用示例
├── figma.yml          # Mock数据
└── package.json
```

## 快速开始

### 基础使用

```javascript
import { createOptimizer } from './src/simple/index.js';

// 1. 创建优化器
const optimizer = await createOptimizer('./figma.yml');

// 2. 获取所有关键词（供LLM选择）
const keywords = optimizer.getAllKeywords();
console.log('Available keywords:', keywords);

// 3. LLM选择相关关键词后进行过滤
const result = optimizer.filterByKeywords(['按钮', '尺寸']);
console.log('Filtered data:', result.data);
console.log('Data reduction:', result.stats.reductionPercentage.components + '%');
```

### LLM集成示例

```javascript
import { OneClickSolution } from './src/simple/index.js';

// 一键式处理用户查询
const result = await OneClickSolution.processQuery(
  './figma.yml',
  '我想查看按钮的大小',
  ['按钮', '尺寸'] // LLM选择的关键词
);

if (result.success) {
  console.log('Found components:', Object.keys(result.data.metadata.components).length);
  console.log('Data reduction:', result.stats.reductionPercentage.components + '%');
}
```

### LLM友好的API

```javascript
import { LLMFriendlyAPI } from './src/simple/index.js';

const api = new LLMFriendlyAPI();
await api.initialize('./figma.yml');

// 1. 获取关键词选项
const options = api.getKeywordOptions('查询按钮大小');
console.log('Recommended:', options.recommended);
console.log('Related:', options.related);

// 2. 执行过滤
const result = api.filter(['按钮', '尺寸']);
console.log('Success:', result.success);
console.log('Component count:', result.summary.componentCount);
```

## 工作流程

1. **关键词提取阶段**
   ```javascript
   // 从figma.yml提取所有关键词
   const keywords = await extractKeywords('./figma.yml');
   console.log('Total keywords:', keywords.totalKeywords);
   ```

2. **LLM选择阶段**
   ```javascript
   // 用户查询："我想查看按钮的大小"
   // LLM分析查询并选择相关关键词：['按钮', '尺寸', '大小']
   ```

3. **数据过滤阶段**
   ```javascript
   // 根据选中的关键词过滤数据
   const filtered = optimizer.filterByKeywords(['按钮', '尺寸']);
   // 结果：只包含名称中包含"按钮"或"尺寸"的组件和节点
   ```

## 优化效果

- **数据量减少**: 典型场景下可减少50-90%的数据传输量
- **响应速度**: 无需复杂计算，过滤速度极快
- **精确匹配**: 基于关键词的精确过滤，结果高度相关
- **灵活配置**: 支持精确匹配、部分匹配、全匹配等多种模式

## 使用场景

1. **LLM应用集成**: 减少向LLM传输的Figma数据量
2. **设计系统查询**: 快速查找特定类型的设计组件
3. **API优化**: 减少Figma API的数据传输成本
4. **移动端应用**: 在带宽受限环境下的数据优化

## 运行示例

```bash
# 运行简化版演示
node examples/simple-usage.js

# 运行完整版演示（如果需要）
node examples/basic-usage.js
```

## API文档

详细的API文档请查看 [docs/api.md](docs/api.md)

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
