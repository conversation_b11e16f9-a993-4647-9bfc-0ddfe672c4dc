# Figma Token Optimizer

一个用于优化Figma Token使用的Node.js项目，通过数据压缩、智能缓存和查询优化来最小化数据传输量。

## 功能特性

- 🚀 **数据压缩**: 使用先进的压缩算法减少数据体积
- 🔍 **智能查询**: 支持按组件类型、属性等快速检索
- 💾 **缓存机制**: 减少重复API调用，提升响应速度
- 🔌 **MCP兼容**: 与FigmaMcp无缝集成
- 📊 **数据优化**: 预处理和索引建立，提升查询效率

## 项目结构

```
figma-token-optimizer/
├── src/
│   ├── core/           # 核心功能模块
│   ├── utils/          # 工具函数
│   ├── adapters/       # MCP适配器
│   └── cache/          # 缓存机制
├── test/               # 测试文件
├── scripts/            # 构建和优化脚本
├── docs/               # 文档
├── figma.yml           # Mock数据
└── package.json
```

## 快速开始

1. 安装依赖
```bash
npm install
```

2. 运行开发模式
```bash
npm run dev
```

3. 运行测试
```bash
npm test
```

## 使用示例

```javascript
import { FigmaOptimizer } from './src/index.js';

const optimizer = new FigmaOptimizer();
await optimizer.loadData('./figma.yml');

// 查询按钮大小
const buttonSizes = await optimizer.query({
  type: 'component',
  category: 'button',
  property: 'size'
});

console.log(buttonSizes);
```

## API文档

详细的API文档请查看 [docs/api.md](docs/api.md)

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
