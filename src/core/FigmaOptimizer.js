import { DataParser } from './DataParser.js';
import { QueryEngine } from './QueryEngine.js';
import { CacheManager } from '../cache/CacheManager.js';
import { DataCompressor } from '../cache/DataCompressor.js';

/**
 * Figma优化器主类
 * 整合所有功能模块，提供统一的API接口
 */
export class FigmaOptimizer {
  constructor(options = {}) {
    const {
      enableCache = true,
      enableCompression = true,
      cacheOptions = {},
      compressionMethod = 'optimized'
    } = options;

    this.parser = new DataParser();
    this.queryEngine = new QueryEngine(this.parser);
    this.cache = enableCache ? new CacheManager(cacheOptions) : null;
    this.compressor = enableCompression ? new DataCompressor() : null;
    this.compressionMethod = compressionMethod;
    
    this.isInitialized = false;
    this.dataSource = null;
    this.optimizationStats = {
      loadTime: 0,
      indexBuildTime: 0,
      totalQueries: 0,
      cacheHits: 0,
      compressionRatio: 0
    };
  }

  /**
   * 初始化优化器，加载数据
   * @param {string} dataSource - 数据源路径或数据对象
   * @returns {Promise<void>}
   */
  async initialize(dataSource) {
    const startTime = Date.now();
    
    try {
      this.dataSource = dataSource;
      
      // 检查缓存中是否有预处理的数据
      const cacheKey = `parsed_data:${this.getDataSourceHash(dataSource)}`;
      let parsedData = null;
      
      if (this.cache) {
        parsedData = this.cache.get(cacheKey);
      }
      
      if (!parsedData) {
        // 加载和解析数据
        if (typeof dataSource === 'string') {
          await this.parser.loadData(dataSource);
        } else {
          this.parser.rawData = dataSource;
          await this.parser.buildIndexes();
        }
        
        // 缓存解析后的数据
        if (this.cache) {
          this.cache.set(cacheKey, {
            rawData: this.parser.rawData,
            componentIndex: Array.from(this.parser.componentIndex.entries()),
            componentSetIndex: Array.from(this.parser.componentSetIndex.entries()),
            nodeIndex: Array.from(this.parser.nodeIndex.entries()),
            propertyIndex: Array.from(this.parser.propertyIndex.entries())
          }, 3600); // 1小时缓存
        }
      } else {
        // 从缓存恢复数据
        this.parser.rawData = parsedData.rawData;
        this.parser.componentIndex = new Map(parsedData.componentIndex);
        this.parser.componentSetIndex = new Map(parsedData.componentSetIndex);
        this.parser.nodeIndex = new Map(parsedData.nodeIndex);
        this.parser.propertyIndex = new Map(parsedData.propertyIndex);
      }
      
      this.isInitialized = true;
      this.optimizationStats.loadTime = Date.now() - startTime;
      
      // 预热常用查询缓存
      if (this.cache) {
        await this.warmupCache();
      }
      
      console.log(`FigmaOptimizer initialized in ${this.optimizationStats.loadTime}ms`);
      
    } catch (error) {
      throw new Error(`Failed to initialize FigmaOptimizer: ${error.message}`);
    }
  }

  /**
   * 执行查询
   * @param {Object} query - 查询对象
   * @returns {Promise<any>} 查询结果
   */
  async query(query) {
    if (!this.isInitialized) {
      throw new Error('FigmaOptimizer not initialized. Call initialize() first.');
    }

    this.optimizationStats.totalQueries++;
    
    // 尝试从缓存获取结果
    let result = null;
    const cacheKey = this.cache ? this.cache.createQueryKey(query) : null;
    
    if (this.cache && cacheKey) {
      result = this.cache.get(cacheKey);
      if (result !== undefined) {
        this.optimizationStats.cacheHits++;
        return result;
      }
    }

    // 执行查询
    result = await this.queryEngine.execute(query);
    
    // 缓存结果
    if (this.cache && cacheKey) {
      this.cache.set(cacheKey, result, 1800); // 30分钟缓存
    }

    return result;
  }

  /**
   * 获取按钮大小信息（常用查询的快捷方法）
   * @returns {Promise<Object>} 按钮大小信息
   */
  async getButtonSizes() {
    return this.query({
      type: 'component',
      category: 'button',
      property: 'size'
    });
  }

  /**
   * 获取按钮颜色信息
   * @returns {Promise<Object>} 按钮颜色信息
   */
  async getButtonColors() {
    return this.query({
      type: 'component',
      category: 'button',
      property: 'color'
    });
  }

  /**
   * 获取组件统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getComponentStats() {
    const cacheKey = this.cache ? this.cache.createStatsKey('components') : null;
    
    if (this.cache && cacheKey) {
      const cached = this.cache.get(cacheKey);
      if (cached !== undefined) {
        return cached;
      }
    }

    const stats = this.queryEngine.getComponentStats();
    
    if (this.cache && cacheKey) {
      this.cache.set(cacheKey, stats, 3600); // 1小时缓存
    }

    return stats;
  }

  /**
   * 搜索组件
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Array>} 搜索结果
   */
  async search(keyword, options = {}) {
    const cacheKey = this.cache ? `search:${keyword}:${JSON.stringify(options)}` : null;
    
    if (this.cache && cacheKey) {
      const cached = this.cache.get(cacheKey);
      if (cached !== undefined) {
        return cached;
      }
    }

    const results = this.queryEngine.search(keyword, options);
    
    if (this.cache && cacheKey) {
      this.cache.set(cacheKey, results, 900); // 15分钟缓存
    }

    return results;
  }

  /**
   * 压缩数据
   * @param {any} data - 要压缩的数据
   * @returns {string} 压缩后的数据
   */
  compressData(data) {
    if (!this.compressor) {
      throw new Error('Compression not enabled');
    }

    const compressed = this.compressor.compress(data, this.compressionMethod);
    const stats = this.compressor.getCompressionStats();
    this.optimizationStats.compressionRatio = stats.compressionRatio;
    
    return compressed;
  }

  /**
   * 解压缩数据
   * @param {string} compressedData - 压缩的数据
   * @returns {any} 解压缩后的数据
   */
  decompressData(compressedData) {
    if (!this.compressor) {
      throw new Error('Compression not enabled');
    }

    return this.compressor.decompress(compressedData, this.compressionMethod);
  }

  /**
   * 获取优化统计信息
   * @returns {Object} 优化统计信息
   */
  getOptimizationStats() {
    const stats = { ...this.optimizationStats };
    
    if (this.cache) {
      const cacheStats = this.cache.getStats();
      stats.cacheHitRate = cacheStats.hitRate;
      stats.cacheSize = this.cache.getSizeInfo();
    }
    
    if (this.parser) {
      stats.dataStats = this.parser.getStats();
    }

    return stats;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    if (this.cache) {
      this.cache.flushAll();
      console.log('Cache cleared');
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache() {
    if (!this.cache) return;

    const commonQueries = {
      'button_sizes': () => this.queryEngine.getButtonSizes(),
      'button_colors': () => this.queryEngine.getButtonColors(),
      'component_stats': () => this.queryEngine.getComponentStats(),
      'all_buttons': () => this.queryEngine.queryComponents('button'),
      'all_components': () => this.queryEngine.queryComponents()
    };

    await this.cache.warmup(commonQueries);
  }

  /**
   * 获取数据源哈希（用于缓存键）
   */
  getDataSourceHash(dataSource) {
    if (typeof dataSource === 'string') {
      return dataSource.replace(/[^a-zA-Z0-9]/g, '_');
    } else {
      // 对于对象，使用简单的哈希
      return JSON.stringify(dataSource).length.toString();
    }
  }

  /**
   * 导出优化后的数据
   * @param {Object} options - 导出选项
   * @returns {Object} 优化后的数据
   */
  exportOptimizedData(options = {}) {
    const { includeNodes = true, compress = true } = options;
    
    const exportData = {
      metadata: this.parser.rawData.metadata,
      stats: this.getOptimizationStats()
    };

    if (includeNodes) {
      exportData.nodes = this.parser.rawData.nodes;
    }

    if (compress && this.compressor) {
      return {
        compressed: true,
        data: this.compressData(exportData),
        compressionStats: this.compressor.getCompressionStats()
      };
    }

    return exportData;
  }

  /**
   * 重新加载数据
   */
  async reload() {
    if (!this.dataSource) {
      throw new Error('No data source to reload from');
    }

    this.isInitialized = false;
    this.clearCache();
    await this.initialize(this.dataSource);
  }

  /**
   * 销毁优化器，清理资源
   */
  destroy() {
    if (this.cache) {
      this.cache.flushAll();
    }
    
    this.parser = null;
    this.queryEngine = null;
    this.cache = null;
    this.compressor = null;
    this.isInitialized = false;
    
    console.log('FigmaOptimizer destroyed');
  }
}
