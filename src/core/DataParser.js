import yaml from 'js-yaml';
import fs from 'fs/promises';

/**
 * Figma数据解析器
 * 负责解析figma.yml文件并建立组件索引
 */
export class DataParser {
  constructor() {
    this.rawData = null;
    this.componentIndex = new Map();
    this.componentSetIndex = new Map();
    this.nodeIndex = new Map();
    this.propertyIndex = new Map();
  }

  /**
   * 加载并解析YAML文件
   * @param {string} filePath - YAML文件路径
   */
  async loadData(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      this.rawData = yaml.load(fileContent);
      await this.buildIndexes();
      return this.rawData;
    } catch (error) {
      throw new Error(`Failed to load data from ${filePath}: ${error.message}`);
    }
  }

  /**
   * 建立各种索引以优化查询性能
   */
  async buildIndexes() {
    if (!this.rawData) {
      throw new Error('No data loaded. Call loadData() first.');
    }

    // 建立组件索引
    this.buildComponentIndex();
    
    // 建立组件集索引
    this.buildComponentSetIndex();
    
    // 建立节点索引
    this.buildNodeIndex();
    
    // 建立属性索引
    this.buildPropertyIndex();
  }

  /**
   * 建立组件索引
   */
  buildComponentIndex() {
    const { components } = this.rawData.metadata;
    
    for (const [id, component] of Object.entries(components)) {
      // 按ID索引
      this.componentIndex.set(id, component);
      
      // 按名称索引
      const nameKey = `name:${component.name}`;
      if (!this.componentIndex.has(nameKey)) {
        this.componentIndex.set(nameKey, []);
      }
      this.componentIndex.get(nameKey).push(component);
      
      // 按组件集ID索引
      if (component.componentSetId) {
        const setKey = `set:${component.componentSetId}`;
        if (!this.componentIndex.has(setKey)) {
          this.componentIndex.set(setKey, []);
        }
        this.componentIndex.get(setKey).push(component);
      }
      
      // 解析组件属性（从名称中提取）
      this.parseComponentProperties(component);
    }
  }

  /**
   * 建立组件集索引
   */
  buildComponentSetIndex() {
    const { componentSets } = this.rawData.metadata;
    
    for (const [id, componentSet] of Object.entries(componentSets)) {
      this.componentSetIndex.set(id, componentSet);
      
      // 按名称索引
      const nameKey = `name:${componentSet.name}`;
      if (!this.componentSetIndex.has(nameKey)) {
        this.componentSetIndex.set(nameKey, []);
      }
      this.componentSetIndex.get(nameKey).push(componentSet);
    }
  }

  /**
   * 建立节点索引
   */
  buildNodeIndex() {
    const { nodes } = this.rawData;
    
    if (nodes && Array.isArray(nodes)) {
      for (const node of nodes) {
        this.indexNode(node);
      }
    }
  }

  /**
   * 递归索引节点
   */
  indexNode(node) {
    // 按ID索引
    this.nodeIndex.set(node.id, node);
    
    // 按类型索引
    const typeKey = `type:${node.type}`;
    if (!this.nodeIndex.has(typeKey)) {
      this.nodeIndex.set(typeKey, []);
    }
    this.nodeIndex.get(typeKey).push(node);
    
    // 按名称索引
    if (node.name) {
      const nameKey = `name:${node.name}`;
      if (!this.nodeIndex.has(nameKey)) {
        this.nodeIndex.set(nameKey, []);
      }
      this.nodeIndex.get(nameKey).push(node);
    }
    
    // 递归处理子节点
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        this.indexNode(child);
      }
    }
  }

  /**
   * 解析组件属性（从组件名称中提取）
   */
  parseComponentProperties(component) {
    const name = component.name;
    
    // 解析按钮相关属性
    if (name.includes('按钮') || name.includes('Button')) {
      this.extractButtonProperties(component, name);
    }
    
    // 解析其他组件类型的属性
    this.extractGeneralProperties(component, name);
  }

  /**
   * 提取按钮属性
   */
  extractButtonProperties(component, name) {
    const properties = {};
    
    // 提取尺寸
    if (name.includes('小按钮')) properties.size = '小';
    else if (name.includes('中按钮')) properties.size = '中';
    else if (name.includes('大按钮')) properties.size = '大';
    
    // 提取宽度
    if (name.includes('推荐宽度=短')) properties.width = '短';
    else if (name.includes('推荐宽度=中')) properties.width = '中';
    else if (name.includes('推荐宽度=长')) properties.width = '长';
    
    // 提取颜色
    if (name.includes('颜色=粉')) properties.color = '粉';
    else if (name.includes('颜色=灰')) properties.color = '灰';
    else if (name.includes('颜色=黄')) properties.color = '黄';
    else if (name.includes('颜色=蓝')) properties.color = '蓝';
    else if (name.includes('颜色=白')) properties.color = '白';
    
    // 提取样式
    if (name.includes('样式=实色')) properties.style = '实色';
    else if (name.includes('样式=线框')) properties.style = '线框';
    else if (name.includes('样式=浅底')) properties.style = '浅底';
    
    // 提取状态
    if (name.includes('状态=正常')) properties.state = '正常';
    else if (name.includes('状态=按下')) properties.state = '按下';
    else if (name.includes('状态=禁用')) properties.state = '禁用';
    
    component.parsedProperties = properties;
    
    // 建立属性索引
    for (const [key, value] of Object.entries(properties)) {
      const propKey = `button:${key}:${value}`;
      if (!this.propertyIndex.has(propKey)) {
        this.propertyIndex.set(propKey, []);
      }
      this.propertyIndex.get(propKey).push(component);
    }
  }

  /**
   * 提取通用属性
   */
  extractGeneralProperties(component, name) {
    // 可以根据需要扩展其他组件类型的属性提取逻辑
    const properties = component.parsedProperties || {};
    
    // 提取方向属性
    if (name.includes('方向=')) {
      const directionMatch = name.match(/方向=([^,，]+)/);
      if (directionMatch) {
        properties.direction = directionMatch[1].trim();
      }
    }
    
    // 提取类型属性
    if (name.includes('类型=')) {
      const typeMatch = name.match(/类型=([^,，]+)/);
      if (typeMatch) {
        properties.componentType = typeMatch[1].trim();
      }
    }
    
    component.parsedProperties = properties;
  }

  /**
   * 建立属性索引
   */
  buildPropertyIndex() {
    // 属性索引在解析组件属性时已经建立
    console.log(`Built property index with ${this.propertyIndex.size} entries`);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalComponents: Object.keys(this.rawData?.metadata?.components || {}).length,
      totalComponentSets: Object.keys(this.rawData?.metadata?.componentSets || {}).length,
      totalNodes: this.nodeIndex.size,
      indexedProperties: this.propertyIndex.size,
      componentIndexSize: this.componentIndex.size,
      nodeIndexSize: this.nodeIndex.size
    };
  }
}
