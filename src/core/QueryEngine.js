/**
 * 查询引擎
 * 提供高效的数据查询功能
 */
export class QueryEngine {
  constructor(dataParser) {
    this.parser = dataParser;
  }

  /**
   * 执行查询
   * @param {Object} query - 查询对象
   * @returns {Array} 查询结果
   */
  async execute(query) {
    const { type, category, property, value, filters = {} } = query;

    switch (type) {
      case 'component':
        return this.queryComponents(category, property, value, filters);
      case 'node':
        return this.queryNodes(category, property, value, filters);
      case 'componentSet':
        return this.queryComponentSets(category, property, value, filters);
      default:
        throw new Error(`Unsupported query type: ${type}`);
    }
  }

  /**
   * 查询组件
   */
  queryComponents(category, property, value, filters) {
    let results = [];

    if (category === 'button') {
      results = this.queryButtonComponents(property, value, filters);
    } else if (category) {
      // 通过名称查找特定类别的组件
      const nameKey = `name:${category}`;
      results = this.parser.componentIndex.get(nameKey) || [];
    } else {
      // 获取所有组件
      results = Array.from(this.parser.componentIndex.values())
        .filter(item => typeof item === 'object' && !Array.isArray(item));
    }

    // 应用额外的过滤器
    return this.applyFilters(results, filters);
  }

  /**
   * 查询按钮组件
   */
  queryButtonComponents(property, value, filters) {
    if (property && value) {
      const propKey = `button:${property}:${value}`;
      return this.parser.propertyIndex.get(propKey) || [];
    }

    // 获取所有按钮组件
    const buttonComponents = [];
    for (const [key, components] of this.parser.propertyIndex.entries()) {
      if (key.startsWith('button:')) {
        buttonComponents.push(...components);
      }
    }

    // 去重
    const uniqueComponents = Array.from(
      new Map(buttonComponents.map(comp => [comp.id, comp])).values()
    );

    return uniqueComponents;
  }

  /**
   * 查询节点
   */
  queryNodes(category, property, value, filters) {
    let results = [];

    if (category) {
      if (category === 'type') {
        const typeKey = `type:${value}`;
        results = this.parser.nodeIndex.get(typeKey) || [];
      } else if (category === 'name') {
        const nameKey = `name:${value}`;
        results = this.parser.nodeIndex.get(nameKey) || [];
      }
    } else {
      // 获取所有节点
      results = Array.from(this.parser.nodeIndex.values())
        .filter(item => typeof item === 'object' && !Array.isArray(item));
    }

    return this.applyFilters(results, filters);
  }

  /**
   * 查询组件集
   */
  queryComponentSets(category, property, value, filters) {
    let results = [];

    if (category === 'name' && value) {
      const nameKey = `name:${value}`;
      results = this.parser.componentSetIndex.get(nameKey) || [];
    } else {
      // 获取所有组件集
      results = Array.from(this.parser.componentSetIndex.values())
        .filter(item => typeof item === 'object' && !Array.isArray(item));
    }

    return this.applyFilters(results, filters);
  }

  /**
   * 应用过滤器
   */
  applyFilters(results, filters) {
    if (!filters || Object.keys(filters).length === 0) {
      return results;
    }

    return results.filter(item => {
      for (const [key, value] of Object.entries(filters)) {
        if (key === 'properties') {
          // 过滤解析的属性
          if (!this.matchProperties(item.parsedProperties, value)) {
            return false;
          }
        } else if (key === 'name') {
          // 名称模糊匹配
          if (!item.name || !item.name.includes(value)) {
            return false;
          }
        } else if (key === 'id') {
          // ID精确匹配
          if (item.id !== value) {
            return false;
          }
        } else {
          // 其他属性匹配
          if (item[key] !== value) {
            return false;
          }
        }
      }
      return true;
    });
  }

  /**
   * 匹配属性
   */
  matchProperties(itemProperties, filterProperties) {
    if (!itemProperties || !filterProperties) {
      return true;
    }

    for (const [key, value] of Object.entries(filterProperties)) {
      if (itemProperties[key] !== value) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取按钮尺寸信息
   * 这是一个常用的查询场景
   */
  getButtonSizes() {
    const sizeMap = new Map();
    
    // 查询所有按钮组件
    const buttons = this.queryComponents('button');
    
    for (const button of buttons) {
      const properties = button.parsedProperties;
      if (properties && properties.size) {
        if (!sizeMap.has(properties.size)) {
          sizeMap.set(properties.size, []);
        }
        sizeMap.get(properties.size).push({
          id: button.id,
          name: button.name,
          properties: properties
        });
      }
    }

    return Object.fromEntries(sizeMap);
  }

  /**
   * 获取按钮颜色信息
   */
  getButtonColors() {
    const colorMap = new Map();
    
    const buttons = this.queryComponents('button');
    
    for (const button of buttons) {
      const properties = button.parsedProperties;
      if (properties && properties.color) {
        if (!colorMap.has(properties.color)) {
          colorMap.set(properties.color, []);
        }
        colorMap.get(properties.color).push({
          id: button.id,
          name: button.name,
          properties: properties
        });
      }
    }

    return Object.fromEntries(colorMap);
  }

  /**
   * 获取组件统计信息
   */
  getComponentStats() {
    const stats = {
      totalComponents: 0,
      componentsByType: {},
      buttonsBySize: {},
      buttonsByColor: {},
      buttonsByStyle: {}
    };

    // 统计所有组件
    for (const [key, value] of this.parser.componentIndex.entries()) {
      if (typeof value === 'object' && !Array.isArray(value)) {
        stats.totalComponents++;
        
        // 统计按钮属性
        if (value.parsedProperties) {
          const props = value.parsedProperties;
          
          if (props.size) {
            stats.buttonsBySize[props.size] = (stats.buttonsBySize[props.size] || 0) + 1;
          }
          
          if (props.color) {
            stats.buttonsByColor[props.color] = (stats.buttonsByColor[props.color] || 0) + 1;
          }
          
          if (props.style) {
            stats.buttonsByStyle[props.style] = (stats.buttonsByStyle[props.style] || 0) + 1;
          }
        }
      }
    }

    return stats;
  }

  /**
   * 搜索功能 - 支持模糊搜索
   */
  search(keyword, options = {}) {
    const { type = 'all', limit = 50 } = options;
    const results = [];
    const lowerKeyword = keyword.toLowerCase();

    // 搜索组件
    if (type === 'all' || type === 'component') {
      for (const [key, value] of this.parser.componentIndex.entries()) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          if (value.name && value.name.toLowerCase().includes(lowerKeyword)) {
            results.push({
              type: 'component',
              item: value,
              relevance: this.calculateRelevance(value.name, keyword)
            });
          }
        }
      }
    }

    // 搜索节点
    if (type === 'all' || type === 'node') {
      for (const [key, value] of this.parser.nodeIndex.entries()) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          if (value.name && value.name.toLowerCase().includes(lowerKeyword)) {
            results.push({
              type: 'node',
              item: value,
              relevance: this.calculateRelevance(value.name, keyword)
            });
          }
        }
      }
    }

    // 按相关性排序并限制结果数量
    return results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit)
      .map(result => result.item);
  }

  /**
   * 计算搜索相关性
   */
  calculateRelevance(text, keyword) {
    const lowerText = text.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();
    
    // 完全匹配得分最高
    if (lowerText === lowerKeyword) return 100;
    
    // 开头匹配得分较高
    if (lowerText.startsWith(lowerKeyword)) return 80;
    
    // 包含匹配得分中等
    if (lowerText.includes(lowerKeyword)) return 60;
    
    return 0;
  }
}
