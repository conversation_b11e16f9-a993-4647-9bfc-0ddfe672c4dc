import NodeCache from 'node-cache';
import { DataCompressor } from './DataCompressor.js';

/**
 * 缓存管理器
 * 提供智能缓存功能，减少重复计算和数据传输
 */
export class CacheManager {
  constructor(options = {}) {
    const {
      stdTTL = 3600, // 默认1小时过期
      checkperiod = 600, // 每10分钟检查过期
      useClones = false,
      deleteOnExpire = true,
      enableCompression = true
    } = options;

    this.cache = new NodeCache({
      stdTTL,
      checkperiod,
      useClones,
      deleteOnExpire
    });

    this.compressor = enableCompression ? new DataCompressor() : null;
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      compressionSavings: 0
    };

    // 监听缓存事件
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    this.cache.on('set', (key, value) => {
      this.stats.sets++;
    });

    this.cache.on('del', (key, value) => {
      this.stats.deletes++;
    });

    this.cache.on('expired', (key, value) => {
      console.log(`Cache key expired: ${key}`);
    });
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键
   * @returns {any} 缓存的数据，如果不存在返回undefined
   */
  get(key) {
    const value = this.cache.get(key);
    
    if (value !== undefined) {
      this.stats.hits++;
      
      // 如果启用了压缩，需要解压缩
      if (this.compressor && value._compressed) {
        return this.compressor.decompress(value.data, value.method);
      }
      
      return value;
    } else {
      this.stats.misses++;
      return undefined;
    }
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键
   * @param {any} value - 要缓存的数据
   * @param {number} ttl - 过期时间（秒），可选
   * @returns {boolean} 是否设置成功
   */
  set(key, value, ttl) {
    let dataToCache = value;

    // 如果启用了压缩且数据较大，进行压缩
    if (this.compressor && this.shouldCompress(value)) {
      const compressed = this.compressor.compress(value);
      const compressionStats = this.compressor.getCompressionStats();
      
      dataToCache = {
        _compressed: true,
        data: compressed,
        method: 'optimized',
        originalSize: compressionStats.originalSize,
        compressedSize: compressionStats.compressedSize
      };

      this.stats.compressionSavings += compressionStats.originalSize - compressionStats.compressedSize;
    }

    return this.cache.set(key, dataToCache, ttl);
  }

  /**
   * 删除缓存数据
   * @param {string} key - 缓存键
   * @returns {number} 删除的键数量
   */
  del(key) {
    return this.cache.del(key);
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  has(key) {
    return this.cache.has(key);
  }

  /**
   * 获取或设置缓存（如果不存在则执行函数并缓存结果）
   * @param {string} key - 缓存键
   * @param {Function} fn - 获取数据的函数
   * @param {number} ttl - 过期时间（秒），可选
   * @returns {any} 缓存的数据或函数执行结果
   */
  async getOrSet(key, fn, ttl) {
    let value = this.get(key);
    
    if (value === undefined) {
      value = await fn();
      this.set(key, value, ttl);
    }
    
    return value;
  }

  /**
   * 批量获取缓存数据
   * @param {string[]} keys - 缓存键数组
   * @returns {Object} 键值对对象
   */
  mget(keys) {
    const result = {};
    
    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        result[key] = value;
      }
    }
    
    return result;
  }

  /**
   * 批量设置缓存数据
   * @param {Object} keyValuePairs - 键值对对象
   * @param {number} ttl - 过期时间（秒），可选
   * @returns {boolean} 是否全部设置成功
   */
  mset(keyValuePairs, ttl) {
    let allSuccess = true;
    
    for (const [key, value] of Object.entries(keyValuePairs)) {
      if (!this.set(key, value, ttl)) {
        allSuccess = false;
      }
    }
    
    return allSuccess;
  }

  /**
   * 清空所有缓存
   */
  flushAll() {
    this.cache.flushAll();
    this.resetStats();
  }

  /**
   * 获取所有缓存键
   * @returns {string[]} 缓存键数组
   */
  keys() {
    return this.cache.keys();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const cacheStats = this.cache.getStats();
    
    return {
      ...this.stats,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      keys: cacheStats.keys,
      ksize: cacheStats.ksize,
      vsize: cacheStats.vsize
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      compressionSavings: 0
    };
  }

  /**
   * 判断是否应该压缩数据
   * @param {any} value - 要判断的数据
   * @returns {boolean} 是否应该压缩
   */
  shouldCompress(value) {
    if (!value) return false;
    
    const jsonString = JSON.stringify(value);
    // 只有当数据大于1KB时才压缩
    return jsonString.length > 1024;
  }

  /**
   * 创建查询缓存键
   * @param {Object} query - 查询对象
   * @returns {string} 缓存键
   */
  createQueryKey(query) {
    const sortedQuery = this.sortObject(query);
    return `query:${JSON.stringify(sortedQuery)}`;
  }

  /**
   * 创建组件缓存键
   * @param {string} componentId - 组件ID
   * @param {string} property - 属性名
   * @returns {string} 缓存键
   */
  createComponentKey(componentId, property = '') {
    return `component:${componentId}${property ? ':' + property : ''}`;
  }

  /**
   * 创建统计缓存键
   * @param {string} type - 统计类型
   * @returns {string} 缓存键
   */
  createStatsKey(type) {
    return `stats:${type}`;
  }

  /**
   * 排序对象（用于创建一致的缓存键）
   * @param {Object} obj - 要排序的对象
   * @returns {Object} 排序后的对象
   */
  sortObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObject(item));
    }
    
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = {};
    
    for (const key of sortedKeys) {
      sortedObj[key] = this.sortObject(obj[key]);
    }
    
    return sortedObj;
  }

  /**
   * 预热缓存 - 预加载常用查询
   * @param {Object} commonQueries - 常用查询对象
   */
  async warmup(commonQueries) {
    console.log('Warming up cache...');
    
    for (const [key, queryFn] of Object.entries(commonQueries)) {
      try {
        await this.getOrSet(key, queryFn, 7200); // 2小时过期
        console.log(`Warmed up cache for: ${key}`);
      } catch (error) {
        console.error(`Failed to warm up cache for ${key}:`, error);
      }
    }
    
    console.log('Cache warmup completed');
  }

  /**
   * 获取缓存大小信息
   * @returns {Object} 大小信息
   */
  getSizeInfo() {
    const stats = this.cache.getStats();
    
    return {
      keyCount: stats.keys,
      keySize: stats.ksize,
      valueSize: stats.vsize,
      totalSize: stats.ksize + stats.vsize,
      compressionSavings: this.stats.compressionSavings
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    // NodeCache会自动清理过期缓存，这里可以添加额外的清理逻辑
    const beforeKeys = this.cache.keys().length;
    
    // 手动触发过期检查
    this.cache.checkData();
    
    const afterKeys = this.cache.keys().length;
    const cleaned = beforeKeys - afterKeys;
    
    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired cache entries`);
    }
    
    return cleaned;
  }
}
