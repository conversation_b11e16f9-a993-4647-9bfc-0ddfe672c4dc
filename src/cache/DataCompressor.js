import LZString from 'lz-string';

/**
 * 数据压缩器
 * 负责压缩和解压缩数据以减少存储空间和传输量
 */
export class DataCompressor {
  constructor() {
    this.compressionStats = {
      originalSize: 0,
      compressedSize: 0,
      compressionRatio: 0
    };
  }

  /**
   * 压缩数据
   * @param {any} data - 要压缩的数据
   * @param {string} method - 压缩方法 ('lz' | 'json' | 'optimized')
   * @returns {string} 压缩后的字符串
   */
  compress(data, method = 'optimized') {
    const jsonString = JSON.stringify(data);
    this.compressionStats.originalSize = jsonString.length;

    let compressed;
    
    switch (method) {
      case 'lz':
        compressed = LZString.compress(jsonString);
        break;
      case 'json':
        compressed = this.compressJSON(data);
        break;
      case 'optimized':
        compressed = this.optimizedCompress(data);
        break;
      default:
        throw new Error(`Unsupported compression method: ${method}`);
    }

    this.compressionStats.compressedSize = compressed.length;
    this.compressionStats.compressionRatio = 
      (1 - this.compressionStats.compressedSize / this.compressionStats.originalSize) * 100;

    return compressed;
  }

  /**
   * 解压缩数据
   * @param {string} compressedData - 压缩的数据
   * @param {string} method - 压缩方法
   * @returns {any} 解压缩后的数据
   */
  decompress(compressedData, method = 'optimized') {
    switch (method) {
      case 'lz':
        const decompressed = LZString.decompress(compressedData);
        return JSON.parse(decompressed);
      case 'json':
        return this.decompressJSON(compressedData);
      case 'optimized':
        return this.optimizedDecompress(compressedData);
      default:
        throw new Error(`Unsupported decompression method: ${method}`);
    }
  }

  /**
   * 优化的压缩方法
   * 结合数据结构优化和LZ压缩
   */
  optimizedCompress(data) {
    // 第一步：优化数据结构
    const optimizedData = this.optimizeDataStructure(data);
    
    // 第二步：LZ压缩
    const jsonString = JSON.stringify(optimizedData);
    return LZString.compress(jsonString);
  }

  /**
   * 优化的解压缩方法
   */
  optimizedDecompress(compressedData) {
    // 第一步：LZ解压缩
    const decompressed = LZString.decompress(compressedData);
    const optimizedData = JSON.parse(decompressed);
    
    // 第二步：还原数据结构
    return this.restoreDataStructure(optimizedData);
  }

  /**
   * 优化数据结构
   * 减少重复数据，使用引用和缩写
   */
  optimizeDataStructure(data) {
    const optimized = {
      meta: this.optimizeMetadata(data.metadata),
      nodes: this.optimizeNodes(data.nodes),
      dict: {} // 字典用于存储重复的字符串
    };

    return optimized;
  }

  /**
   * 优化元数据
   */
  optimizeMetadata(metadata) {
    if (!metadata) return null;

    const optimized = {
      name: metadata.name,
      lastModified: metadata.lastModified,
      thumbnailUrl: this.shortenUrl(metadata.thumbnailUrl),
      comps: this.optimizeComponents(metadata.components),
      sets: this.optimizeComponentSets(metadata.componentSets)
    };

    return optimized;
  }

  /**
   * 优化组件数据
   */
  optimizeComponents(components) {
    if (!components) return null;

    const optimized = {};
    
    for (const [id, component] of Object.entries(components)) {
      optimized[id] = {
        i: component.id,
        k: component.key,
        n: this.abbreviateName(component.name),
        s: component.componentSetId
      };
    }

    return optimized;
  }

  /**
   * 优化组件集数据
   */
  optimizeComponentSets(componentSets) {
    if (!componentSets) return null;

    const optimized = {};
    
    for (const [id, componentSet] of Object.entries(componentSets)) {
      optimized[id] = {
        i: componentSet.id,
        k: componentSet.key,
        n: this.abbreviateName(componentSet.name),
        d: componentSet.description ? this.abbreviateDescription(componentSet.description) : null
      };
    }

    return optimized;
  }

  /**
   * 优化节点数据
   */
  optimizeNodes(nodes) {
    if (!nodes || !Array.isArray(nodes)) return null;

    return nodes.map(node => this.optimizeNode(node));
  }

  /**
   * 优化单个节点
   */
  optimizeNode(node) {
    const optimized = {
      i: node.id,
      n: this.abbreviateName(node.name),
      t: this.abbreviateType(node.type)
    };

    // 只保留必要的属性
    if (node.layout) optimized.l = node.layout;
    if (node.fills) optimized.f = node.fills;
    if (node.text) optimized.txt = node.text;
    if (node.textStyle) optimized.ts = node.textStyle;
    if (node.borderRadius) optimized.br = node.borderRadius;
    if (node.componentId) optimized.ci = node.componentId;
    if (node.componentProperties) optimized.cp = this.optimizeComponentProperties(node.componentProperties);

    // 递归处理子节点
    if (node.children && Array.isArray(node.children)) {
      optimized.c = node.children.map(child => this.optimizeNode(child));
    }

    return optimized;
  }

  /**
   * 优化组件属性
   */
  optimizeComponentProperties(properties) {
    if (!properties || !Array.isArray(properties)) return null;

    return properties.map(prop => ({
      n: prop.name,
      v: prop.value,
      t: this.abbreviatePropertyType(prop.type)
    }));
  }

  /**
   * 还原数据结构
   */
  restoreDataStructure(optimizedData) {
    const restored = {
      metadata: this.restoreMetadata(optimizedData.meta),
      nodes: this.restoreNodes(optimizedData.nodes)
    };

    return restored;
  }

  /**
   * 还原元数据
   */
  restoreMetadata(optimizedMeta) {
    if (!optimizedMeta) return null;

    return {
      name: optimizedMeta.name,
      lastModified: optimizedMeta.lastModified,
      thumbnailUrl: optimizedMeta.thumbnailUrl,
      components: this.restoreComponents(optimizedMeta.comps),
      componentSets: this.restoreComponentSets(optimizedMeta.sets)
    };
  }

  /**
   * 还原组件数据
   */
  restoreComponents(optimizedComps) {
    if (!optimizedComps) return null;

    const restored = {};
    
    for (const [id, comp] of Object.entries(optimizedComps)) {
      restored[id] = {
        id: comp.i,
        key: comp.k,
        name: comp.n,
        componentSetId: comp.s
      };
    }

    return restored;
  }

  /**
   * 还原组件集数据
   */
  restoreComponentSets(optimizedSets) {
    if (!optimizedSets) return null;

    const restored = {};
    
    for (const [id, set] of Object.entries(optimizedSets)) {
      restored[id] = {
        id: set.i,
        key: set.k,
        name: set.n,
        description: set.d || ''
      };
    }

    return restored;
  }

  /**
   * 还原节点数据
   */
  restoreNodes(optimizedNodes) {
    if (!optimizedNodes || !Array.isArray(optimizedNodes)) return null;

    return optimizedNodes.map(node => this.restoreNode(node));
  }

  /**
   * 还原单个节点
   */
  restoreNode(optimizedNode) {
    const restored = {
      id: optimizedNode.i,
      name: optimizedNode.n,
      type: this.restoreType(optimizedNode.t)
    };

    // 还原其他属性
    if (optimizedNode.l) restored.layout = optimizedNode.l;
    if (optimizedNode.f) restored.fills = optimizedNode.f;
    if (optimizedNode.txt) restored.text = optimizedNode.txt;
    if (optimizedNode.ts) restored.textStyle = optimizedNode.ts;
    if (optimizedNode.br) restored.borderRadius = optimizedNode.br;
    if (optimizedNode.ci) restored.componentId = optimizedNode.ci;
    if (optimizedNode.cp) restored.componentProperties = this.restoreComponentProperties(optimizedNode.cp);

    // 还原子节点
    if (optimizedNode.c && Array.isArray(optimizedNode.c)) {
      restored.children = optimizedNode.c.map(child => this.restoreNode(child));
    }

    return restored;
  }

  /**
   * 还原组件属性
   */
  restoreComponentProperties(optimizedProps) {
    if (!optimizedProps || !Array.isArray(optimizedProps)) return null;

    return optimizedProps.map(prop => ({
      name: prop.n,
      value: prop.v,
      type: this.restorePropertyType(prop.t)
    }));
  }

  // 辅助方法
  abbreviateName(name) {
    if (!name) return name;
    // 可以实现更复杂的名称缩写逻辑
    return name.length > 50 ? name.substring(0, 47) + '...' : name;
  }

  abbreviateDescription(description) {
    if (!description) return description;
    return description.length > 100 ? description.substring(0, 97) + '...' : description;
  }

  abbreviateType(type) {
    const typeMap = {
      'FRAME': 'F',
      'TEXT': 'T',
      'RECTANGLE': 'R',
      'ELLIPSE': 'E',
      'INSTANCE': 'I',
      'GROUP': 'G',
      'IMAGE-SVG': 'S'
    };
    return typeMap[type] || type;
  }

  restoreType(abbreviatedType) {
    const typeMap = {
      'F': 'FRAME',
      'T': 'TEXT',
      'R': 'RECTANGLE',
      'E': 'ELLIPSE',
      'I': 'INSTANCE',
      'G': 'GROUP',
      'S': 'IMAGE-SVG'
    };
    return typeMap[abbreviatedType] || abbreviatedType;
  }

  abbreviatePropertyType(type) {
    const typeMap = {
      'VARIANT': 'V',
      'BOOLEAN': 'B',
      'TEXT': 'T'
    };
    return typeMap[type] || type;
  }

  restorePropertyType(abbreviatedType) {
    const typeMap = {
      'V': 'VARIANT',
      'B': 'BOOLEAN',
      'T': 'TEXT'
    };
    return typeMap[abbreviatedType] || abbreviatedType;
  }

  shortenUrl(url) {
    if (!url) return url;
    // 简化URL，只保留关键部分
    return url.includes('figma.com') ? url.split('?')[0] : url;
  }

  compressJSON(data) {
    // 简单的JSON压缩，移除空格
    return JSON.stringify(data);
  }

  decompressJSON(compressedData) {
    return JSON.parse(compressedData);
  }

  /**
   * 获取压缩统计信息
   */
  getCompressionStats() {
    return { ...this.compressionStats };
  }
}
