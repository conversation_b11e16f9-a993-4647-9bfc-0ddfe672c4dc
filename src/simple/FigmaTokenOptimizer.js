import { KeywordExtractor } from './KeywordExtractor.js';
import { KeywordFilter } from './KeywordFilter.js';

/**
 * Figma Token 优化器 - 简化版
 * 基于关键词过滤的轻量级解决方案
 */
export class FigmaTokenOptimizer {
  constructor() {
    this.extractor = new KeywordExtractor();
    this.filter = null;
    this.rawData = null;
    this.allKeywords = [];
    this.isInitialized = false;
  }

  /**
   * 初始化优化器
   * @param {string} filePath - figma.yml文件路径
   */
  async initialize(filePath) {
    try {
      console.log('🚀 Initializing Figma Token Optimizer...');
      
      // 加载数据并提取关键词
      const keywordSummary = await this.extractor.loadAndExtract(filePath);
      this.rawData = this.extractor.rawData;
      this.allKeywords = keywordSummary.keywords;
      
      // 创建过滤器
      this.filter = new KeywordFilter(this.rawData);
      
      this.isInitialized = true;
      
      console.log('✅ Initialization completed');
      console.log(`📊 Found ${keywordSummary.totalKeywords} unique keywords`);
      
      return keywordSummary;
    } catch (error) {
      throw new Error(`Initialization failed: ${error.message}`);
    }
  }

  /**
   * 获取所有关键词
   * @returns {Array} 关键词数组
   */
  getAllKeywords() {
    this.checkInitialized();
    return this.allKeywords;
  }

  /**
   * 获取高频关键词
   * @param {number} limit - 限制数量
   * @returns {Array} 高频关键词数组
   */
  getTopKeywords(limit = 20) {
    this.checkInitialized();
    return this.extractor.getTopKeywords(limit);
  }

  /**
   * 按类别获取关键词
   * @returns {Object} 分类后的关键词
   */
  getKeywordsByCategory() {
    this.checkInitialized();
    return this.extractor.getKeywordsByCategory();
  }

  /**
   * 搜索关键词
   * @param {string} searchTerm - 搜索词
   * @returns {Array} 匹配的关键词
   */
  searchKeywords(searchTerm) {
    this.checkInitialized();
    return this.extractor.searchKeywords(searchTerm);
  }

  /**
   * 根据关键词过滤数据
   * @param {Array} filterKeywords - 要过滤的关键词数组
   * @param {Object} options - 过滤选项
   * @returns {Object} 包含过滤后数据和统计信息的对象
   */
  filterByKeywords(filterKeywords, options = {}) {
    this.checkInitialized();
    
    if (!filterKeywords || filterKeywords.length === 0) {
      throw new Error('Filter keywords cannot be empty');
    }

    console.log(`🔍 Filtering data with keywords: ${filterKeywords.join(', ')}`);
    
    const filteredData = this.filter.filterByKeywords(filterKeywords, options);
    const stats = this.filter.getFilterStats(this.rawData, filteredData);
    
    console.log(`📉 Data reduction: Components ${stats.reductionPercentage.components}%, Nodes ${stats.reductionPercentage.nodes}%`);
    
    return {
      data: filteredData,
      stats: stats,
      filterKeywords: filterKeywords,
      options: options
    };
  }

  /**
   * 智能关键词推荐
   * @param {Array} selectedKeywords - 已选关键词
   * @returns {Array} 推荐的关键词
   */
  suggestKeywords(selectedKeywords) {
    this.checkInitialized();
    return this.filter.suggestRelatedKeywords(selectedKeywords, this.allKeywords);
  }

  /**
   * 为LLM提供关键词选择接口
   * @param {string} userQuery - 用户查询
   * @returns {Object} 关键词选择建议
   */
  getKeywordSuggestionsForLLM(userQuery) {
    this.checkInitialized();
    
    const lowerQuery = userQuery.toLowerCase();
    const suggestions = {
      recommended: [],
      related: [],
      all: this.allKeywords
    };

    // 基于查询内容推荐关键词
    const queryKeywords = this.extractKeywordsFromQuery(lowerQuery);
    
    // 找到直接匹配的关键词
    for (const keyword of this.allKeywords) {
      if (lowerQuery.includes(keyword.toLowerCase())) {
        suggestions.recommended.push(keyword);
      }
    }

    // 找到相关的关键词
    for (const queryKeyword of queryKeywords) {
      const related = this.filter.suggestRelatedKeywords([queryKeyword], this.allKeywords);
      suggestions.related.push(...related);
    }

    // 去重
    suggestions.recommended = [...new Set(suggestions.recommended)];
    suggestions.related = [...new Set(suggestions.related)].filter(
      keyword => !suggestions.recommended.includes(keyword)
    );

    return suggestions;
  }

  /**
   * 从查询中提取关键词
   * @param {string} query - 查询字符串
   * @returns {Array} 提取的关键词
   */
  extractKeywordsFromQuery(query) {
    const keywords = [];
    
    // 常见的查询模式
    const patterns = [
      /按钮/g, /button/g,
      /图标/g, /icon/g,
      /颜色/g, /color/g,
      /大小/g, /size/g,
      /尺寸/g,
      /样式/g, /style/g,
      /状态/g, /state/g
    ];

    patterns.forEach(pattern => {
      const matches = query.match(pattern);
      if (matches) {
        keywords.push(...matches);
      }
    });

    return [...new Set(keywords)];
  }

  /**
   * 生成优化报告
   * @param {Object} filterResult - 过滤结果
   * @returns {Object} 优化报告
   */
  generateOptimizationReport(filterResult) {
    const { data, stats, filterKeywords, options } = filterResult;
    
    const originalSize = JSON.stringify(this.rawData).length;
    const filteredSize = JSON.stringify(data).length;
    const sizeReduction = ((originalSize - filteredSize) / originalSize * 100).toFixed(2);

    return {
      summary: {
        filterKeywords: filterKeywords,
        options: options,
        timestamp: new Date().toISOString()
      },
      dataReduction: {
        components: {
          original: stats.original.components,
          filtered: stats.filtered.components,
          reduction: stats.reduction.components,
          percentage: stats.reductionPercentage.components + '%'
        },
        nodes: {
          original: stats.original.nodes,
          filtered: stats.filtered.nodes,
          reduction: stats.reduction.nodes,
          percentage: stats.reductionPercentage.nodes + '%'
        },
        dataSize: {
          original: `${Math.round(originalSize / 1024)}KB`,
          filtered: `${Math.round(filteredSize / 1024)}KB`,
          reduction: sizeReduction + '%'
        }
      },
      recommendations: this.generateRecommendations(stats, filterKeywords)
    };
  }

  /**
   * 生成优化建议
   * @param {Object} stats - 统计信息
   * @param {Array} filterKeywords - 过滤关键词
   * @returns {Array} 建议列表
   */
  generateRecommendations(stats, filterKeywords) {
    const recommendations = [];

    if (stats.reductionPercentage.components < 50) {
      recommendations.push('Consider adding more specific keywords to further reduce data size');
    }

    if (stats.filtered.components === 0) {
      recommendations.push('No components match the selected keywords. Try broader or different keywords');
    }

    if (filterKeywords.length === 1) {
      recommendations.push('Consider combining multiple related keywords for better filtering');
    }

    if (stats.reductionPercentage.nodes > 90) {
      recommendations.push('Excellent data reduction achieved! This filter is very effective');
    }

    return recommendations;
  }

  /**
   * 导出过滤后的数据
   * @param {Object} filterResult - 过滤结果
   * @param {string} format - 导出格式 ('json' | 'yaml')
   * @returns {string} 导出的数据字符串
   */
  exportFilteredData(filterResult, format = 'json') {
    const { data } = filterResult;
    
    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else if (format === 'yaml') {
      // 如果需要YAML格式，可以在这里添加yaml序列化
      return JSON.stringify(data, null, 2); // 暂时返回JSON
    } else {
      throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    if (!this.isInitialized) {
      throw new Error('Optimizer not initialized. Call initialize() first.');
    }
  }

  /**
   * 获取优化器状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      totalKeywords: this.allKeywords.length,
      dataLoaded: !!this.rawData,
      originalDataSize: this.rawData ? `${Math.round(JSON.stringify(this.rawData).length / 1024)}KB` : null
    };
  }

  /**
   * 重置优化器
   */
  reset() {
    this.extractor = new KeywordExtractor();
    this.filter = null;
    this.rawData = null;
    this.allKeywords = [];
    this.isInitialized = false;
  }
}
