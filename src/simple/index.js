import { FigmaTokenOptimizer } from './FigmaTokenOptimizer.js';
import { KeywordExtractor } from './KeywordExtractor.js';
import { KeywordFilter } from './KeywordFilter.js';

// 导出主要类
export {
  FigmaTokenOptimizer,
  KeywordExtractor,
  KeywordFilter
};

/**
 * 快速创建优化器实例
 * @param {string} filePath - figma.yml文件路径
 * @returns {Promise<FigmaTokenOptimizer>} 初始化后的优化器实例
 */
export async function createOptimizer(filePath = './figma.yml') {
  const optimizer = new FigmaTokenOptimizer();
  await optimizer.initialize(filePath);
  return optimizer;
}

/**
 * 快速关键词提取
 * @param {string} filePath - figma.yml文件路径
 * @returns {Promise<Object>} 关键词摘要
 */
export async function extractKeywords(filePath = './figma.yml') {
  const extractor = new KeywordExtractor();
  return await extractor.loadAndExtract(filePath);
}

/**
 * 快速数据过滤
 * @param {string} filePath - figma.yml文件路径
 * @param {Array} keywords - 过滤关键词
 * @param {Object} options - 过滤选项
 * @returns {Promise<Object>} 过滤结果
 */
export async function quickFilter(filePath, keywords, options = {}) {
  const optimizer = await createOptimizer(filePath);
  return optimizer.filterByKeywords(keywords, options);
}

/**
 * LLM友好的API接口
 * 专为LLM调用设计的简化接口
 */
export class LLMFriendlyAPI {
  constructor() {
    this.optimizer = null;
    this.isReady = false;
  }

  /**
   * 初始化API
   * @param {string} filePath - figma.yml文件路径
   */
  async initialize(filePath = './figma.yml') {
    this.optimizer = await createOptimizer(filePath);
    this.isReady = true;
    return {
      success: true,
      message: 'API initialized successfully',
      totalKeywords: this.optimizer.getAllKeywords().length
    };
  }

  /**
   * 获取关键词选项（供LLM选择）
   * @param {string} userQuery - 用户查询
   * @returns {Object} 关键词选项
   */
  getKeywordOptions(userQuery = '') {
    if (!this.isReady) {
      throw new Error('API not initialized');
    }

    if (userQuery) {
      return this.optimizer.getKeywordSuggestionsForLLM(userQuery);
    } else {
      return {
        all: this.optimizer.getAllKeywords(),
        top: this.optimizer.getTopKeywords(20).map(k => k.keyword),
        categories: this.optimizer.getKeywordsByCategory()
      };
    }
  }

  /**
   * 执行过滤（LLM选择关键词后调用）
   * @param {Array} selectedKeywords - LLM选择的关键词
   * @param {Object} options - 过滤选项
   * @returns {Object} 过滤结果
   */
  filter(selectedKeywords, options = {}) {
    if (!this.isReady) {
      throw new Error('API not initialized');
    }

    const result = this.optimizer.filterByKeywords(selectedKeywords, {
      matchMode: 'any',
      includePartialMatch: true,
      ...options
    });

    // 返回LLM友好的格式
    return {
      success: true,
      data: result.data,
      summary: {
        keywords: selectedKeywords,
        componentCount: Object.keys(result.data.metadata.components).length,
        nodeCount: result.stats.filtered.nodes,
        reductionPercentage: result.stats.reductionPercentage.components,
        dataSize: `${Math.round(JSON.stringify(result.data).length / 1024)}KB`
      },
      optimization: result.stats
    };
  }

  /**
   * 搜索关键词
   * @param {string} searchTerm - 搜索词
   * @returns {Array} 匹配的关键词
   */
  searchKeywords(searchTerm) {
    if (!this.isReady) {
      throw new Error('API not initialized');
    }

    return this.optimizer.searchKeywords(searchTerm);
  }

  /**
   * 获取API状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      ready: this.isReady,
      optimizer: this.isReady ? this.optimizer.getStatus() : null
    };
  }
}

/**
 * 创建LLM友好的API实例
 * @param {string} filePath - figma.yml文件路径
 * @returns {Promise<LLMFriendlyAPI>} API实例
 */
export async function createLLMAPI(filePath = './figma.yml') {
  const api = new LLMFriendlyAPI();
  await api.initialize(filePath);
  return api;
}

/**
 * 一键式解决方案
 * 最简单的使用方式
 */
export class OneClickSolution {
  /**
   * 处理用户查询的完整流程
   * @param {string} filePath - figma.yml文件路径
   * @param {string} userQuery - 用户查询
   * @param {Array} llmSelectedKeywords - LLM选择的关键词
   * @returns {Promise<Object>} 完整的处理结果
   */
  static async processQuery(filePath, userQuery, llmSelectedKeywords) {
    try {
      // 1. 初始化优化器
      const optimizer = await createOptimizer(filePath);

      // 2. 获取关键词建议（如果LLM没有提供关键词）
      let keywords = llmSelectedKeywords;
      if (!keywords || keywords.length === 0) {
        const suggestions = optimizer.getKeywordSuggestionsForLLM(userQuery);
        keywords = suggestions.recommended.slice(0, 3); // 取前3个推荐关键词
      }

      // 3. 执行过滤
      const filterResult = optimizer.filterByKeywords(keywords, {
        matchMode: 'any',
        includePartialMatch: true
      });

      // 4. 生成报告
      const report = optimizer.generateOptimizationReport(filterResult);

      // 5. 返回完整结果
      return {
        success: true,
        query: userQuery,
        keywords: keywords,
        data: filterResult.data,
        stats: filterResult.stats,
        report: report,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        query: userQuery,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 批量处理查询
   * @param {string} filePath - figma.yml文件路径
   * @param {Array} queries - 查询数组，每个查询包含 {query, keywords}
   * @returns {Promise<Array>} 处理结果数组
   */
  static async processBatchQueries(filePath, queries) {
    const results = [];
    
    for (const queryItem of queries) {
      const result = await this.processQuery(
        filePath, 
        queryItem.query, 
        queryItem.keywords
      );
      results.push(result);
    }

    return results;
  }
}

// 如果直接运行此文件，执行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    try {
      console.log('🚀 Figma Token Optimizer - Simple API Demo\n');

      // 演示一键式解决方案
      console.log('📝 One-Click Solution Demo:');
      const result = await OneClickSolution.processQuery(
        './figma.yml',
        '我想查看按钮的大小',
        ['按钮', '尺寸'] // LLM选择的关键词
      );

      if (result.success) {
        console.log('✅ Query processed successfully');
        console.log(`🔍 Keywords used: ${result.keywords.join(', ')}`);
        console.log(`📊 Found ${Object.keys(result.data.metadata.components).length} components`);
        console.log(`📉 Data reduction: ${result.stats.reductionPercentage.components}%`);
        console.log(`💾 Final data size: ${Math.round(JSON.stringify(result.data).length / 1024)}KB`);
      } else {
        console.log('❌ Query failed:', result.error);
      }

      console.log('\n🎯 This simple API is perfect for LLM integration!');
      console.log('💡 Usage: OneClickSolution.processQuery(filePath, userQuery, llmKeywords)');

    } catch (error) {
      console.error('Demo failed:', error);
      process.exit(1);
    }
  })();
}
