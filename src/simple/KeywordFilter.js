/**
 * 关键词过滤器
 * 根据选中的关键词过滤相关数据
 */
export class KeywordFilter {
  constructor(rawData) {
    this.rawData = rawData;
  }

  /**
   * 根据关键词过滤数据
   * @param {Array} filterKeywords - 要过滤的关键词数组
   * @param {Object} options - 过滤选项
   * @returns {Object} 过滤后的数据
   */
  filterByKeywords(filterKeywords, options = {}) {
    const {
      matchMode = 'any', // 'any' | 'all' - 匹配模式
      caseSensitive = false, // 是否区分大小写
      includeNodes = true, // 是否包含节点数据
      includePartialMatch = true // 是否包含部分匹配
    } = options;

    if (!filterKeywords || filterKeywords.length === 0) {
      return this.rawData; // 如果没有过滤关键词，返回原始数据
    }

    const normalizedKeywords = this.normalizeKeywords(filterKeywords, caseSensitive);
    
    const filteredData = {
      metadata: {
        name: this.rawData.metadata?.name,
        lastModified: this.rawData.metadata?.lastModified,
        thumbnailUrl: this.rawData.metadata?.thumbnailUrl,
        components: this.filterComponents(normalizedKeywords, matchMode, caseSensitive, includePartialMatch),
        componentSets: this.filterComponentSets(normalizedKeywords, matchMode, caseSensitive, includePartialMatch)
      }
    };

    if (includeNodes && this.rawData.nodes) {
      filteredData.nodes = this.filterNodes(this.rawData.nodes, normalizedKeywords, matchMode, caseSensitive, includePartialMatch);
    }

    return filteredData;
  }

  /**
   * 标准化关键词
   * @param {Array} keywords - 关键词数组
   * @param {boolean} caseSensitive - 是否区分大小写
   * @returns {Array} 标准化后的关键词
   */
  normalizeKeywords(keywords, caseSensitive) {
    return keywords.map(keyword => 
      caseSensitive ? keyword.trim() : keyword.trim().toLowerCase()
    );
  }

  /**
   * 过滤组件
   * @param {Array} keywords - 关键词数组
   * @param {string} matchMode - 匹配模式
   * @param {boolean} caseSensitive - 是否区分大小写
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {Object} 过滤后的组件
   */
  filterComponents(keywords, matchMode, caseSensitive, includePartialMatch) {
    if (!this.rawData.metadata?.components) return {};

    const filteredComponents = {};

    for (const [id, component] of Object.entries(this.rawData.metadata.components)) {
      if (this.matchesKeywords(component.name, keywords, matchMode, caseSensitive, includePartialMatch)) {
        filteredComponents[id] = component;
      }
    }

    return filteredComponents;
  }

  /**
   * 过滤组件集
   * @param {Array} keywords - 关键词数组
   * @param {string} matchMode - 匹配模式
   * @param {boolean} caseSensitive - 是否区分大小写
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {Object} 过滤后的组件集
   */
  filterComponentSets(keywords, matchMode, caseSensitive, includePartialMatch) {
    if (!this.rawData.metadata?.componentSets) return {};

    const filteredComponentSets = {};

    for (const [id, componentSet] of Object.entries(this.rawData.metadata.componentSets)) {
      if (this.matchesKeywords(componentSet.name, keywords, matchMode, caseSensitive, includePartialMatch)) {
        filteredComponentSets[id] = componentSet;
      }
    }

    return filteredComponentSets;
  }

  /**
   * 过滤节点
   * @param {Array} nodes - 节点数组
   * @param {Array} keywords - 关键词数组
   * @param {string} matchMode - 匹配模式
   * @param {boolean} caseSensitive - 是否区分大小写
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {Array} 过滤后的节点
   */
  filterNodes(nodes, keywords, matchMode, caseSensitive, includePartialMatch) {
    if (!Array.isArray(nodes)) return [];

    const filteredNodes = [];

    for (const node of nodes) {
      const filteredNode = this.filterSingleNode(node, keywords, matchMode, caseSensitive, includePartialMatch);
      if (filteredNode) {
        filteredNodes.push(filteredNode);
      }
    }

    return filteredNodes;
  }

  /**
   * 过滤单个节点
   * @param {Object} node - 节点对象
   * @param {Array} keywords - 关键词数组
   * @param {string} matchMode - 匹配模式
   * @param {boolean} caseSensitive - 是否区分大小写
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {Object|null} 过滤后的节点或null
   */
  filterSingleNode(node, keywords, matchMode, caseSensitive, includePartialMatch) {
    const nodeMatches = this.matchesKeywords(node.name, keywords, matchMode, caseSensitive, includePartialMatch);
    
    // 递归过滤子节点
    let filteredChildren = null;
    if (node.children && Array.isArray(node.children)) {
      filteredChildren = this.filterNodes(node.children, keywords, matchMode, caseSensitive, includePartialMatch);
    }

    // 如果当前节点匹配或有匹配的子节点，则保留
    if (nodeMatches || (filteredChildren && filteredChildren.length > 0)) {
      const filteredNode = { ...node };
      if (filteredChildren && filteredChildren.length > 0) {
        filteredNode.children = filteredChildren;
      } else if (filteredChildren && filteredChildren.length === 0) {
        // 如果原来有子节点但过滤后没有了，删除children属性
        delete filteredNode.children;
      }
      return filteredNode;
    }

    return null;
  }

  /**
   * 检查名称是否匹配关键词
   * @param {string} name - 名称
   * @param {Array} keywords - 关键词数组
   * @param {string} matchMode - 匹配模式
   * @param {boolean} caseSensitive - 是否区分大小写
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {boolean} 是否匹配
   */
  matchesKeywords(name, keywords, matchMode, caseSensitive, includePartialMatch) {
    if (!name || keywords.length === 0) return false;

    const normalizedName = caseSensitive ? name : name.toLowerCase();

    if (matchMode === 'all') {
      // 所有关键词都必须匹配
      return keywords.every(keyword => 
        this.nameContainsKeyword(normalizedName, keyword, includePartialMatch)
      );
    } else {
      // 任意关键词匹配即可
      return keywords.some(keyword => 
        this.nameContainsKeyword(normalizedName, keyword, includePartialMatch)
      );
    }
  }

  /**
   * 检查名称是否包含关键词
   * @param {string} name - 名称
   * @param {string} keyword - 关键词
   * @param {boolean} includePartialMatch - 是否包含部分匹配
   * @returns {boolean} 是否包含
   */
  nameContainsKeyword(name, keyword, includePartialMatch) {
    if (includePartialMatch) {
      // 部分匹配：名称中包含关键词即可
      return name.includes(keyword);
    } else {
      // 精确匹配：关键词必须作为独立的词出现
      const wordBoundaryRegex = new RegExp(`\\b${this.escapeRegExp(keyword)}\\b`, 'i');
      return wordBoundaryRegex.test(name);
    }
  }

  /**
   * 转义正则表达式特殊字符
   * @param {string} string - 字符串
   * @returns {string} 转义后的字符串
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 获取过滤统计信息
   * @param {Object} originalData - 原始数据
   * @param {Object} filteredData - 过滤后的数据
   * @returns {Object} 统计信息
   */
  getFilterStats(originalData, filteredData) {
    const originalStats = this.getDataStats(originalData);
    const filteredStats = this.getDataStats(filteredData);

    return {
      original: originalStats,
      filtered: filteredStats,
      reduction: {
        components: originalStats.components - filteredStats.components,
        componentSets: originalStats.componentSets - filteredStats.componentSets,
        nodes: originalStats.nodes - filteredStats.nodes
      },
      reductionPercentage: {
        components: originalStats.components > 0 ? 
          ((originalStats.components - filteredStats.components) / originalStats.components * 100).toFixed(2) : 0,
        componentSets: originalStats.componentSets > 0 ? 
          ((originalStats.componentSets - filteredStats.componentSets) / originalStats.componentSets * 100).toFixed(2) : 0,
        nodes: originalStats.nodes > 0 ? 
          ((originalStats.nodes - filteredStats.nodes) / originalStats.nodes * 100).toFixed(2) : 0
      }
    };
  }

  /**
   * 获取数据统计信息
   * @param {Object} data - 数据对象
   * @returns {Object} 统计信息
   */
  getDataStats(data) {
    return {
      components: Object.keys(data.metadata?.components || {}).length,
      componentSets: Object.keys(data.metadata?.componentSets || {}).length,
      nodes: this.countNodes(data.nodes || [])
    };
  }

  /**
   * 递归计算节点数量
   * @param {Array} nodes - 节点数组
   * @returns {number} 节点数量
   */
  countNodes(nodes) {
    if (!Array.isArray(nodes)) return 0;

    let count = nodes.length;
    for (const node of nodes) {
      if (node.children && Array.isArray(node.children)) {
        count += this.countNodes(node.children);
      }
    }
    return count;
  }

  /**
   * 智能关键词建议
   * 基于已选关键词推荐相关关键词
   * @param {Array} selectedKeywords - 已选关键词
   * @param {Array} allKeywords - 所有可用关键词
   * @returns {Array} 推荐的关键词
   */
  suggestRelatedKeywords(selectedKeywords, allKeywords) {
    if (!selectedKeywords || selectedKeywords.length === 0) {
      return [];
    }

    const suggestions = new Set();
    
    // 关键词关联规则
    const associations = {
      '按钮': ['button', '尺寸', 'size', '颜色', 'color', '状态', 'state'],
      'button': ['按钮', '尺寸', 'size', '颜色', 'color', '状态', 'state'],
      '图标': ['icon', '大小', 'size'],
      'icon': ['图标', '大小', 'size'],
      '颜色': ['color', '红', '蓝', '绿', '黄', '粉', '灰', '白', '黑'],
      'color': ['颜色', 'red', 'blue', 'green', 'yellow', 'pink', 'gray', 'white', 'black'],
      '尺寸': ['size', '大', '中', '小', 'large', 'medium', 'small'],
      'size': ['尺寸', '大', '中', '小', 'large', 'medium', 'small']
    };

    for (const keyword of selectedKeywords) {
      const related = associations[keyword] || [];
      related.forEach(relatedKeyword => {
        if (allKeywords.includes(relatedKeyword) && !selectedKeywords.includes(relatedKeyword)) {
          suggestions.add(relatedKeyword);
        }
      });
    }

    return Array.from(suggestions);
  }
}
