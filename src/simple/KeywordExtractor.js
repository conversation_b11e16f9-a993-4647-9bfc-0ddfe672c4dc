import fs from 'fs/promises';

/**
 * 关键词提取器
 * 从figma.yml的name字段提取所有关键词
 */
export class KeywordExtractor {
  constructor() {
    this.rawData = null;
    this.allKeywords = new Set();
    this.keywordFrequency = new Map();
  }

  /**
   * 加载数据并提取关键词
   * @param {string} filePath - YAML文件路径
   */
  async loadAndExtract(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      // 简单的YAML解析（仅支持基本结构）
      this.rawData = this.parseSimpleYAML(fileContent);
      this.extractKeywords();
      return this.getKeywordSummary();
    } catch (error) {
      throw new Error(`Failed to load and extract keywords: ${error.message}`);
    }
  }

  /**
   * 简单的YAML解析器（仅支持基本结构）
   * @param {string} yamlContent - YAML内容
   * @returns {Object} 解析后的对象
   */
  parseSimpleYAML(yamlContent) {
    try {
      // 尝试JSON解析（如果文件实际是JSON格式）
      return JSON.parse(yamlContent);
    } catch {
      // 简单的YAML解析实现
      console.log('📄 Parsing YAML file...');

      const lines = yamlContent.split('\n');
      const result = {
        metadata: {
          name: '',
          lastModified: '',
          thumbnailUrl: '',
          components: {},
          componentSets: {}
        },
        nodes: []
      };

      let currentSection = null;
      let currentComponent = null;
      let currentComponentId = null;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        if (!trimmedLine || trimmedLine.startsWith('#')) continue;

        // 计算缩进级别
        const currentIndent = line.length - line.trimStart().length;

        // 检测主要部分
        if (trimmedLine === 'metadata:') {
          currentSection = 'metadata';
          continue;
        } else if (trimmedLine === 'components:') {
          currentSection = 'components';
          continue;
        } else if (trimmedLine === 'componentSets:') {
          currentSection = 'componentSets';
          continue;
        } else if (trimmedLine === 'nodes:') {
          currentSection = 'nodes';
          continue;
        }

        // 解析metadata部分
        if (currentSection === 'metadata' && currentIndent === 2) {
          if (trimmedLine.startsWith('name:')) {
            result.metadata.name = trimmedLine.split('name:')[1].trim();
          } else if (trimmedLine.startsWith('lastModified:')) {
            result.metadata.lastModified = trimmedLine.split('lastModified:')[1].trim();
          }
        }

        // 解析components部分
        if (currentSection === 'components') {
          if (currentIndent === 4 && trimmedLine.endsWith(':')) {
            // 新的组件ID
            currentComponentId = trimmedLine.slice(1, -2); // 移除引号和冒号
            currentComponent = { id: currentComponentId };
          } else if (currentIndent === 6 && currentComponent) {
            // 组件属性
            if (trimmedLine.startsWith('id:')) {
              currentComponent.id = trimmedLine.split('id:')[1].trim().replace(/'/g, '');
            } else if (trimmedLine.startsWith('name:')) {
              currentComponent.name = trimmedLine.split('name:')[1].trim();
            } else if (trimmedLine.startsWith('componentSetId:')) {
              currentComponent.componentSetId = trimmedLine.split('componentSetId:')[1].trim().replace(/'/g, '');
            }

            // 如果有name属性，保存组件
            if (currentComponent.name && currentComponentId) {
              result.metadata.components[currentComponentId] = { ...currentComponent };
            }
          }
        }
      }

      console.log(`✅ Parsed ${Object.keys(result.metadata.components).length} components`);
      return result;
    }
  }

  /**
   * 提取所有关键词
   */
  extractKeywords() {
    this.allKeywords.clear();
    this.keywordFrequency.clear();

    // 从组件名称提取关键词
    if (this.rawData.metadata?.components) {
      for (const component of Object.values(this.rawData.metadata.components)) {
        this.extractFromName(component.name);
      }
    }

    // 从组件集名称提取关键词
    if (this.rawData.metadata?.componentSets) {
      for (const componentSet of Object.values(this.rawData.metadata.componentSets)) {
        this.extractFromName(componentSet.name);
      }
    }

    // 从节点名称提取关键词
    if (this.rawData.nodes) {
      this.extractFromNodes(this.rawData.nodes);
    }
  }

  /**
   * 从单个名称提取关键词
   * @param {string} name - 名称字符串
   */
  extractFromName(name) {
    if (!name) return;

    // 分割策略：按常见分隔符分割
    const delimiters = /[/\-_=,，\s()[\]【】]/;
    const parts = name.split(delimiters);

    for (let part of parts) {
      part = part.trim();
      if (part.length > 0) {
        // 过滤掉纯数字和单字符
        if (part.length > 1 && !/^\d+$/.test(part)) {
          this.addKeyword(part);
        }
      }
    }

    // 特殊处理：提取中文词汇
    const chineseWords = this.extractChineseWords(name);
    chineseWords.forEach(word => this.addKeyword(word));

    // 特殊处理：提取英文单词
    const englishWords = this.extractEnglishWords(name);
    englishWords.forEach(word => this.addKeyword(word));
  }

  /**
   * 添加关键词并统计频率
   * @param {string} keyword - 关键词
   */
  addKeyword(keyword) {
    // 标准化关键词
    const normalized = this.normalizeKeyword(keyword);
    if (normalized) {
      this.allKeywords.add(normalized);
      this.keywordFrequency.set(normalized, (this.keywordFrequency.get(normalized) || 0) + 1);
    }
  }

  /**
   * 标准化关键词
   * @param {string} keyword - 原始关键词
   * @returns {string|null} 标准化后的关键词
   */
  normalizeKeyword(keyword) {
    if (!keyword) return null;
    
    // 去除首尾空格
    keyword = keyword.trim();
    
    // 过滤掉太短或太长的词
    if (keyword.length < 2 || keyword.length > 20) return null;
    
    // 过滤掉纯数字
    if (/^\d+$/.test(keyword)) return null;
    
    // 过滤掉特殊符号
    if (/^[^\w\u4e00-\u9fff]+$/.test(keyword)) return null;
    
    return keyword;
  }

  /**
   * 提取中文词汇
   * @param {string} text - 文本
   * @returns {Array} 中文词汇数组
   */
  extractChineseWords(text) {
    const words = [];
    
    // 常见的中文词汇模式
    const patterns = [
      /按钮/g,
      /图标/g,
      /文字/g,
      /颜色/g,
      /尺寸/g,
      /大小/g,
      /样式/g,
      /状态/g,
      /背景/g,
      /边框/g,
      /阴影/g,
      /圆角/g,
      /间距/g,
      /布局/g,
      /容器/g,
      /导航/g,
      /菜单/g,
      /列表/g,
      /卡片/g,
      /表单/g,
      /输入/g,
      /选择/g,
      /开关/g,
      /滑块/g,
      /进度/g,
      /加载/g,
      /弹窗/g,
      /提示/g,
      /警告/g,
      /成功/g,
      /错误/g,
      /信息/g
    ];

    patterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        words.push(...matches);
      }
    });

    return [...new Set(words)]; // 去重
  }

  /**
   * 提取英文单词
   * @param {string} text - 文本
   * @returns {Array} 英文单词数组
   */
  extractEnglishWords(text) {
    const words = [];
    
    // 常见的英文词汇模式
    const patterns = [
      /button/gi,
      /icon/gi,
      /text/gi,
      /color/gi,
      /size/gi,
      /style/gi,
      /state/gi,
      /background/gi,
      /border/gi,
      /shadow/gi,
      /radius/gi,
      /margin/gi,
      /padding/gi,
      /layout/gi,
      /container/gi,
      /navigation/gi,
      /menu/gi,
      /list/gi,
      /card/gi,
      /form/gi,
      /input/gi,
      /select/gi,
      /switch/gi,
      /slider/gi,
      /progress/gi,
      /loading/gi,
      /modal/gi,
      /tooltip/gi,
      /alert/gi,
      /success/gi,
      /error/gi,
      /warning/gi,
      /info/gi
    ];

    patterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        words.push(...matches.map(m => m.toLowerCase()));
      }
    });

    return [...new Set(words)]; // 去重
  }

  /**
   * 递归处理节点
   * @param {Array} nodes - 节点数组
   */
  extractFromNodes(nodes) {
    if (!Array.isArray(nodes)) return;

    for (const node of nodes) {
      if (node.name) {
        this.extractFromName(node.name);
      }
      
      // 递归处理子节点
      if (node.children) {
        this.extractFromNodes(node.children);
      }
    }
  }

  /**
   * 获取关键词摘要
   * @returns {Object} 关键词摘要信息
   */
  getKeywordSummary() {
    const sortedKeywords = Array.from(this.keywordFrequency.entries())
      .sort((a, b) => b[1] - a[1]); // 按频率降序排列

    return {
      totalKeywords: this.allKeywords.size,
      keywords: Array.from(this.allKeywords).sort(),
      topKeywords: sortedKeywords.slice(0, 20), // 前20个高频关键词
      keywordFrequency: Object.fromEntries(this.keywordFrequency)
    };
  }

  /**
   * 获取所有关键词
   * @returns {Array} 关键词数组
   */
  getAllKeywords() {
    return Array.from(this.allKeywords).sort();
  }

  /**
   * 获取高频关键词
   * @param {number} limit - 限制数量
   * @returns {Array} 高频关键词数组
   */
  getTopKeywords(limit = 20) {
    return Array.from(this.keywordFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([keyword, frequency]) => ({ keyword, frequency }));
  }

  /**
   * 搜索关键词
   * @param {string} searchTerm - 搜索词
   * @returns {Array} 匹配的关键词
   */
  searchKeywords(searchTerm) {
    const lowerSearchTerm = searchTerm.toLowerCase();
    return Array.from(this.allKeywords)
      .filter(keyword => keyword.toLowerCase().includes(lowerSearchTerm))
      .sort();
  }

  /**
   * 按类别分组关键词
   * @returns {Object} 分组后的关键词
   */
  getKeywordsByCategory() {
    const categories = {
      components: [], // 组件相关
      properties: [], // 属性相关
      states: [],     // 状态相关
      colors: [],     // 颜色相关
      sizes: [],      // 尺寸相关
      others: []      // 其他
    };

    const componentKeywords = ['按钮', 'button', '图标', 'icon', '文字', 'text', '容器', 'container', '导航', 'navigation'];
    const propertyKeywords = ['颜色', 'color', '尺寸', 'size', '样式', 'style', '背景', 'background'];
    const stateKeywords = ['正常', 'normal', '按下', 'pressed', '禁用', 'disabled', '悬停', 'hover'];
    const colorKeywords = ['红', 'red', '蓝', 'blue', '绿', 'green', '黄', 'yellow', '粉', 'pink', '灰', 'gray', '白', 'white', '黑', 'black'];
    const sizeKeywords = ['大', 'large', '中', 'medium', '小', 'small', '长', 'long', '短', 'short'];

    for (const keyword of this.allKeywords) {
      const lowerKeyword = keyword.toLowerCase();
      
      if (componentKeywords.some(ck => lowerKeyword.includes(ck.toLowerCase()))) {
        categories.components.push(keyword);
      } else if (propertyKeywords.some(pk => lowerKeyword.includes(pk.toLowerCase()))) {
        categories.properties.push(keyword);
      } else if (stateKeywords.some(sk => lowerKeyword.includes(sk.toLowerCase()))) {
        categories.states.push(keyword);
      } else if (colorKeywords.some(ck => lowerKeyword.includes(ck.toLowerCase()))) {
        categories.colors.push(keyword);
      } else if (sizeKeywords.some(sk => lowerKeyword.includes(sk.toLowerCase()))) {
        categories.sizes.push(keyword);
      } else {
        categories.others.push(keyword);
      }
    }

    // 排序每个类别
    Object.keys(categories).forEach(category => {
      categories[category].sort();
    });

    return categories;
  }
}
