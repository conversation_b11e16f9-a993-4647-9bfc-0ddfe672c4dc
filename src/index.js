import { FigmaOptimizer } from './core/FigmaOptimizer.js';
import { McpAdapter } from './adapters/McpAdapter.js';
import { DataParser } from './core/DataParser.js';
import { QueryEngine } from './core/QueryEngine.js';
import { CacheManager } from './cache/CacheManager.js';
import { DataCompressor } from './cache/DataCompressor.js';

// 导出所有主要类
export {
  FigmaOptimizer,
  McpAdapter,
  DataParser,
  QueryEngine,
  CacheManager,
  DataCompressor
};

/**
 * 创建默认的FigmaOptimizer实例
 * @param {Object} options - 配置选项
 * @returns {FigmaOptimizer} 优化器实例
 */
export function createOptimizer(options = {}) {
  return new FigmaOptimizer(options);
}

/**
 * 创建MCP适配器实例
 * @param {Object} options - 配置选项
 * @returns {McpAdapter} MCP适配器实例
 */
export function createMcpAdapter(options = {}) {
  return new McpAdapter(options);
}

/**
 * 快速启动函数 - 用于演示和测试
 */
export async function quickStart(dataPath = './figma.yml') {
  console.log('🚀 Starting Figma Token Optimizer...');
  
  try {
    // 创建优化器实例
    const optimizer = createOptimizer({
      enableCache: true,
      enableCompression: true,
      cacheOptions: {
        stdTTL: 3600, // 1小时缓存
        checkperiod: 600 // 10分钟检查周期
      }
    });

    // 初始化
    console.log('📊 Loading and parsing data...');
    await optimizer.initialize(dataPath);

    // 显示统计信息
    const stats = optimizer.getOptimizationStats();
    console.log('📈 Optimization Stats:', {
      loadTime: `${stats.loadTime}ms`,
      totalComponents: stats.dataStats.totalComponents,
      totalNodes: stats.dataStats.totalNodes,
      indexedProperties: stats.dataStats.indexedProperties
    });

    // 演示查询功能
    console.log('\n🔍 Running demo queries...');
    
    // 查询按钮大小
    const buttonSizes = await optimizer.getButtonSizes();
    console.log('按钮尺寸统计:', Object.keys(buttonSizes));

    // 查询按钮颜色
    const buttonColors = await optimizer.getButtonColors();
    console.log('按钮颜色统计:', Object.keys(buttonColors));

    // 搜索功能
    const searchResults = await optimizer.search('按钮', { limit: 5 });
    console.log(`搜索"按钮"结果数量: ${searchResults.length}`);

    // 显示缓存统计
    if (optimizer.cache) {
      const cacheStats = optimizer.cache.getStats();
      console.log('💾 Cache Stats:', {
        hitRate: `${(cacheStats.hitRate * 100).toFixed(2)}%`,
        keys: cacheStats.keys,
        compressionSavings: `${Math.round(cacheStats.compressionSavings / 1024)}KB`
      });
    }

    console.log('\n✅ Quick start completed successfully!');
    return optimizer;

  } catch (error) {
    console.error('❌ Quick start failed:', error.message);
    throw error;
  }
}

/**
 * 演示MCP适配器功能
 */
export async function demoMcpAdapter(dataPath = './figma.yml') {
  console.log('🔌 Starting MCP Adapter Demo...');
  
  try {
    // 创建MCP适配器
    const adapter = createMcpAdapter({
      enableCache: true,
      enableCompression: true
    });

    // 初始化
    await adapter.initialize(dataPath);

    // 演示不同类型的查询
    console.log('\n📋 Testing MCP queries...');

    // 1. 自然语言查询
    const nlResult = await adapter.handleRequest({
      method: 'nlQuery',
      params: { query: '按钮大小' }
    });
    console.log('自然语言查询结果:', nlResult.success ? '成功' : '失败');

    // 2. 结构化查询
    const structuredResult = await adapter.handleRequest({
      method: 'query',
      params: {
        type: 'component',
        category: 'button',
        property: 'color'
      }
    });
    console.log('结构化查询结果:', structuredResult.success ? '成功' : '失败');

    // 3. 搜索查询
    const searchResult = await adapter.handleRequest({
      method: 'search',
      params: {
        keyword: '按钮',
        options: { limit: 3 }
      }
    });
    console.log('搜索查询结果:', searchResult.success ? '成功' : '失败');

    // 4. 统计信息查询
    const statsResult = await adapter.handleRequest({
      method: 'getStats',
      params: { type: 'components' }
    });
    console.log('统计查询结果:', statsResult.success ? '成功' : '失败');

    // 显示适配器状态
    const status = adapter.getStatus();
    console.log('\n📊 MCP Adapter Status:', {
      initialized: status.initialized,
      supportedMethods: status.supportedMethods.length,
      queryPatterns: status.queryPatterns.length
    });

    console.log('\n✅ MCP Adapter demo completed successfully!');
    return adapter;

  } catch (error) {
    console.error('❌ MCP Adapter demo failed:', error.message);
    throw error;
  }
}

// 如果直接运行此文件，执行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    try {
      console.log('🎯 Figma Token Optimizer - Demo Mode\n');
      
      // 运行快速启动演示
      const optimizer = await quickStart();
      
      console.log('\n' + '='.repeat(50) + '\n');
      
      // 运行MCP适配器演示
      const adapter = await demoMcpAdapter();
      
      console.log('\n🎉 All demos completed successfully!');
      console.log('\n💡 Usage examples:');
      console.log('  import { createOptimizer, createMcpAdapter } from "./src/index.js";');
      console.log('  const optimizer = createOptimizer();');
      console.log('  await optimizer.initialize("./figma.yml");');
      console.log('  const buttonSizes = await optimizer.getButtonSizes();');
      
      // 清理资源
      optimizer.destroy();
      adapter.destroy();
      
    } catch (error) {
      console.error('Demo failed:', error);
      process.exit(1);
    }
  })();
}
