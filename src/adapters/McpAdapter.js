import { FigmaOptimizer } from '../core/FigmaOptimizer.js';

/**
 * MCP适配器
 * 提供与FigmaMcp兼容的接口，支持常见查询场景
 */
export class McpAdapter {
  constructor(options = {}) {
    this.optimizer = new FigmaOptimizer(options);
    this.isInitialized = false;
    
    // 定义常见的查询模式
    this.queryPatterns = {
      buttonSize: /按钮.*?大小|button.*?size/i,
      buttonColor: /按钮.*?颜色|button.*?color/i,
      buttonStyle: /按钮.*?样式|button.*?style/i,
      componentInfo: /组件.*?信息|component.*?info/i,
      designToken: /设计.*?令牌|design.*?token/i
    };
  }

  /**
   * 初始化适配器
   * @param {string} dataSource - 数据源路径
   */
  async initialize(dataSource) {
    await this.optimizer.initialize(dataSource);
    this.isInitialized = true;
    console.log('MCP Adapter initialized');
  }

  /**
   * 处理MCP查询请求
   * @param {Object} request - MCP请求对象
   * @returns {Promise<Object>} 响应对象
   */
  async handleRequest(request) {
    if (!this.isInitialized) {
      throw new Error('MCP Adapter not initialized');
    }

    const { method, params } = request;

    try {
      switch (method) {
        case 'query':
          return await this.handleQuery(params);
        case 'search':
          return await this.handleSearch(params);
        case 'getComponent':
          return await this.handleGetComponent(params);
        case 'getStats':
          return await this.handleGetStats(params);
        case 'nlQuery':
          return await this.handleNaturalLanguageQuery(params);
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 处理结构化查询
   * @param {Object} params - 查询参数
   */
  async handleQuery(params) {
    const { type, category, property, value, filters } = params;
    
    const result = await this.optimizer.query({
      type,
      category,
      property,
      value,
      filters
    });

    return {
      success: true,
      data: result,
      metadata: {
        queryType: 'structured',
        resultCount: Array.isArray(result) ? result.length : 1,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 处理搜索请求
   * @param {Object} params - 搜索参数
   */
  async handleSearch(params) {
    const { keyword, options = {} } = params;
    
    const results = await this.optimizer.search(keyword, options);

    return {
      success: true,
      data: results,
      metadata: {
        queryType: 'search',
        keyword,
        resultCount: results.length,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 处理获取组件信息请求
   * @param {Object} params - 组件参数
   */
  async handleGetComponent(params) {
    const { componentId, includeProperties = true } = params;
    
    const component = this.optimizer.parser.componentIndex.get(componentId);
    
    if (!component) {
      throw new Error(`Component not found: ${componentId}`);
    }

    const result = {
      ...component
    };

    if (includeProperties && component.parsedProperties) {
      result.properties = component.parsedProperties;
    }

    return {
      success: true,
      data: result,
      metadata: {
        queryType: 'component',
        componentId,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 处理获取统计信息请求
   * @param {Object} params - 统计参数
   */
  async handleGetStats(params) {
    const { type = 'all' } = params;
    
    let stats;
    
    switch (type) {
      case 'components':
        stats = await this.optimizer.getComponentStats();
        break;
      case 'optimization':
        stats = this.optimizer.getOptimizationStats();
        break;
      case 'all':
        stats = {
          components: await this.optimizer.getComponentStats(),
          optimization: this.optimizer.getOptimizationStats()
        };
        break;
      default:
        throw new Error(`Unsupported stats type: ${type}`);
    }

    return {
      success: true,
      data: stats,
      metadata: {
        queryType: 'stats',
        statsType: type,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 处理自然语言查询
   * @param {Object} params - 自然语言查询参数
   */
  async handleNaturalLanguageQuery(params) {
    const { query } = params;
    
    // 解析自然语言查询
    const parsedQuery = this.parseNaturalLanguageQuery(query);
    
    if (!parsedQuery) {
      throw new Error('Unable to understand the query');
    }

    // 执行解析后的查询
    const result = await this.optimizer.query(parsedQuery);

    return {
      success: true,
      data: result,
      metadata: {
        queryType: 'naturalLanguage',
        originalQuery: query,
        parsedQuery,
        resultCount: Array.isArray(result) ? result.length : 1,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 解析自然语言查询
   * @param {string} query - 自然语言查询
   * @returns {Object|null} 解析后的查询对象
   */
  parseNaturalLanguageQuery(query) {
    const lowerQuery = query.toLowerCase();

    // 按钮大小查询
    if (this.queryPatterns.buttonSize.test(query)) {
      return {
        type: 'component',
        category: 'button',
        property: 'size'
      };
    }

    // 按钮颜色查询
    if (this.queryPatterns.buttonColor.test(query)) {
      return {
        type: 'component',
        category: 'button',
        property: 'color'
      };
    }

    // 按钮样式查询
    if (this.queryPatterns.buttonStyle.test(query)) {
      return {
        type: 'component',
        category: 'button',
        property: 'style'
      };
    }

    // 组件信息查询
    if (this.queryPatterns.componentInfo.test(query)) {
      return {
        type: 'component'
      };
    }

    // 特定尺寸的按钮
    if (lowerQuery.includes('小按钮') || lowerQuery.includes('small button')) {
      return {
        type: 'component',
        category: 'button',
        property: 'size',
        value: '小'
      };
    }

    if (lowerQuery.includes('中按钮') || lowerQuery.includes('medium button')) {
      return {
        type: 'component',
        category: 'button',
        property: 'size',
        value: '中'
      };
    }

    if (lowerQuery.includes('大按钮') || lowerQuery.includes('large button')) {
      return {
        type: 'component',
        category: 'button',
        property: 'size',
        value: '大'
      };
    }

    // 特定颜色的按钮
    const colorMap = {
      '粉色': '粉',
      '灰色': '灰',
      '黄色': '黄',
      '蓝色': '蓝',
      '白色': '白',
      'pink': '粉',
      'gray': '灰',
      'yellow': '黄',
      'blue': '蓝',
      'white': '白'
    };

    for (const [colorName, colorValue] of Object.entries(colorMap)) {
      if (lowerQuery.includes(colorName)) {
        return {
          type: 'component',
          category: 'button',
          property: 'color',
          value: colorValue
        };
      }
    }

    // 如果无法解析，返回搜索查询
    return null;
  }

  /**
   * 获取常用查询的快捷方法
   */
  getQuickQueries() {
    return {
      buttonSizes: () => this.handleRequest({
        method: 'nlQuery',
        params: { query: '按钮大小' }
      }),
      
      buttonColors: () => this.handleRequest({
        method: 'nlQuery',
        params: { query: '按钮颜色' }
      }),
      
      smallButtons: () => this.handleRequest({
        method: 'nlQuery',
        params: { query: '小按钮' }
      }),
      
      componentStats: () => this.handleRequest({
        method: 'getStats',
        params: { type: 'components' }
      })
    };
  }

  /**
   * 批量处理请求
   * @param {Array} requests - 请求数组
   * @returns {Promise<Array>} 响应数组
   */
  async handleBatchRequests(requests) {
    const results = [];
    
    for (const request of requests) {
      try {
        const result = await this.handleRequest(request);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          request: request
        });
      }
    }

    return results;
  }

  /**
   * 获取适配器状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      optimizerStats: this.isInitialized ? this.optimizer.getOptimizationStats() : null,
      supportedMethods: [
        'query',
        'search',
        'getComponent',
        'getStats',
        'nlQuery'
      ],
      queryPatterns: Object.keys(this.queryPatterns)
    };
  }

  /**
   * 重置适配器
   */
  async reset() {
    if (this.optimizer) {
      await this.optimizer.reload();
    }
  }

  /**
   * 销毁适配器
   */
  destroy() {
    if (this.optimizer) {
      this.optimizer.destroy();
    }
    this.isInitialized = false;
  }
}
