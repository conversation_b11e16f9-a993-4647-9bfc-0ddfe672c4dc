import fs from 'fs/promises';

/**
 * 函数1: 从figma.yml数据中提取所有关键词
 * @param {string} yamlFilePath - figma.yml文件路径
 * @returns {Promise<Array>} 所有关键词数组
 */
export async function extractKeywords(yamlFilePath) {
  try {
    // 读取YAML文件
    const fileContent = await fs.readFile(yamlFilePath, 'utf8');
    const data = parseYAML(fileContent);
    
    const keywords = new Set();
    
    // 从组件名称提取关键词
    if (data.metadata?.components) {
      for (const component of Object.values(data.metadata.components)) {
        if (component.name) {
          extractFromName(component.name, keywords);
        }
      }
    }
    
    // 从组件集名称提取关键词
    if (data.metadata?.componentSets) {
      for (const componentSet of Object.values(data.metadata.componentSets)) {
        if (componentSet.name) {
          extractFromName(componentSet.name, keywords);
        }
      }
    }
    
    // 从节点名称提取关键词
    if (data.nodes) {
      extractFromNodes(data.nodes, keywords);
    }
    
    return Array.from(keywords).sort();
  } catch (error) {
    throw new Error(`Failed to extract keywords: ${error.message}`);
  }
}

/**
 * 函数2: 根据关键词过滤figma数据
 * @param {string} yamlFilePath - figma.yml文件路径
 * @param {Array} expectedKeywords - LLM过滤出的关键词数组
 * @returns {Promise<Object>} 过滤后的figma数据
 */
export async function filterFigmaData(yamlFilePath, expectedKeywords) {
  try {
    // 读取YAML文件
    const fileContent = await fs.readFile(yamlFilePath, 'utf8');
    const data = parseYAML(fileContent);
    
    if (!expectedKeywords || expectedKeywords.length === 0) {
      return data; // 如果没有关键词，返回原始数据
    }
    
    // 标准化关键词（转小写）
    const normalizedKeywords = expectedKeywords.map(k => k.toLowerCase());
    
    // 过滤后的数据结构
    const filteredData = {
      metadata: {
        name: data.metadata?.name,
        lastModified: data.metadata?.lastModified,
        thumbnailUrl: data.metadata?.thumbnailUrl,
        components: {},
        componentSets: {}
      },
      nodes: []
    };
    
    // 过滤组件
    if (data.metadata?.components) {
      for (const [id, component] of Object.entries(data.metadata.components)) {
        if (matchesKeywords(component.name, normalizedKeywords)) {
          filteredData.metadata.components[id] = component;
        }
      }
    }
    
    // 过滤组件集
    if (data.metadata?.componentSets) {
      for (const [id, componentSet] of Object.entries(data.metadata.componentSets)) {
        if (matchesKeywords(componentSet.name, normalizedKeywords)) {
          filteredData.metadata.componentSets[id] = componentSet;
        }
      }
    }
    
    // 过滤节点
    if (data.nodes) {
      filteredData.nodes = filterNodes(data.nodes, normalizedKeywords);
    }
    
    return filteredData;
  } catch (error) {
    throw new Error(`Failed to filter figma data: ${error.message}`);
  }
}

// ============ 辅助函数 ============

/**
 * 简单的YAML解析
 */
function parseYAML(yamlContent) {
  try {
    return JSON.parse(yamlContent);
  } catch {
    // 简化的YAML解析
    const lines = yamlContent.split('\n');
    const result = {
      metadata: { name: '', lastModified: '', thumbnailUrl: '', components: {}, componentSets: {} },
      nodes: []
    };

    let currentSection = null;
    let currentComponent = null;
    let currentComponentId = null;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine || trimmedLine.startsWith('#')) continue;

      const currentIndent = line.length - line.trimStart().length;

      if (trimmedLine === 'metadata:') currentSection = 'metadata';
      else if (trimmedLine === 'components:') currentSection = 'components';
      else if (trimmedLine === 'componentSets:') currentSection = 'componentSets';
      else if (trimmedLine === 'nodes:') currentSection = 'nodes';

      if (currentSection === 'components') {
        if (currentIndent === 4 && trimmedLine.endsWith(':')) {
          currentComponentId = trimmedLine.slice(1, -2);
          currentComponent = { id: currentComponentId };
        } else if (currentIndent === 6 && currentComponent) {
          if (trimmedLine.startsWith('name:')) {
            currentComponent.name = trimmedLine.split('name:')[1].trim();
            if (currentComponentId) {
              result.metadata.components[currentComponentId] = { ...currentComponent };
            }
          }
        }
      }
    }
    
    console.log(JSON.stringify(result).strin)
    return result;
  }
}

/**
 * 从名称中提取关键词
 */
function extractFromName(name, keywords) {
  if (!name) return;
  
  // 按分隔符分割
  const delimiters = /[/\-_=,，\s()[\]【】]/;
  const parts = name.split(delimiters);
  
  for (let part of parts) {
    part = part.trim();
    if (part.length > 1 && !/^\d+$/.test(part)) {
      keywords.add(part);
    }
  }
}

/**
 * 从节点中提取关键词
 */
function extractFromNodes(nodes, keywords) {
  if (!Array.isArray(nodes)) return;
  
  for (const node of nodes) {
    if (node.name) {
      extractFromName(node.name, keywords);
    }
    if (node.children) {
      extractFromNodes(node.children, keywords);
    }
  }
}

/**
 * 检查名称是否匹配关键词
 */
function matchesKeywords(name, keywords) {
  if (!name) return false;
  
  const lowerName = name.toLowerCase();
  return keywords.some(keyword => lowerName.includes(keyword));
}

/**
 * 过滤节点
 */
function filterNodes(nodes, keywords) {
  if (!Array.isArray(nodes)) return [];
  
  const filteredNodes = [];
  
  for (const node of nodes) {
    const nodeMatches = matchesKeywords(node.name, keywords);
    let filteredChildren = null;
    
    if (node.children) {
      filteredChildren = filterNodes(node.children, keywords);
    }
    
    if (nodeMatches || (filteredChildren && filteredChildren.length > 0)) {
      const filteredNode = { ...node };
      if (filteredChildren && filteredChildren.length > 0) {
        filteredNode.children = filteredChildren;
      } else if (filteredChildren && filteredChildren.length === 0) {
        delete filteredNode.children;
      }
      filteredNodes.push(filteredNode);
    }
  }
  
  return filteredNodes;
}

// ============ 演示用法 ============

async function demo() {
  try {
    console.log('🚀 Simple Figma Optimizer Demo\n');
    
    // 1. 提取所有关键词
    console.log('📋 Step 1: Extract all keywords');
    const keywords = await extractKeywords('./figma.yml');
    console.log(`✅ Found ${keywords.length} keywords:`, keywords.slice(0, 10), '...');
    console.log();
    
    // 2. 模拟LLM根据用户问题选择关键词
    console.log('🤖 Step 2: LLM selects keywords based on user query');
    const userQuery = '我想查看按钮的大小';
    const llmSelectedKeywords = ['按钮', '尺寸']; // LLM根据用户问题选择的关键词
    console.log(`User query: "${userQuery}"`);
    console.log(`LLM selected keywords:`, llmSelectedKeywords);
    console.log();
    
    // 3. 根据关键词过滤数据
    console.log('🔍 Step 3: Filter figma data');
    const filteredData = await filterFigmaData('./figma.yml', llmSelectedKeywords);
    
    const originalCount = Object.keys((await parseYAML(await fs.readFile('./figma.yml', 'utf8'))).metadata.components).length;
    const filteredCount = Object.keys(filteredData.metadata.components).length;
    const reduction = ((originalCount - filteredCount) / originalCount * 100).toFixed(2);
    
    console.log(`✅ Filtered from ${originalCount} to ${filteredCount} components (${reduction}% reduction)`);
    console.log(`📦 Data size: ${Math.round(JSON.stringify(filteredData).length / 1024)}KB`);
    
    // 显示一些过滤结果
    console.log('\n📋 Sample filtered components:');
    Object.values(filteredData.metadata.components).slice(0, 3).forEach((comp, i) => {
      console.log(`  ${i + 1}. ${comp.name}`);
    });
    
  } catch (error) {
    console.error('❌ Demo failed:', error.message);
  }
}

// 如果直接运行此文件，执行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  demo();
}
