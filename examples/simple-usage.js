import { FigmaTokenOptimizer } from '../src/simple/FigmaTokenOptimizer.js';

/**
 * 简化版使用示例
 * 演示基于关键词过滤的轻量级方案
 */
async function simpleUsageDemo() {
  console.log('🎯 Figma Token Optimizer - Simple Usage Demo\n');

  try {
    // 1. 创建优化器实例
    const optimizer = new FigmaTokenOptimizer();

    // 2. 初始化并加载数据
    console.log('📊 Loading data and extracting keywords...');
    const keywordSummary = await optimizer.initialize('./figma.yml');
    
    console.log(`✅ Found ${keywordSummary.totalKeywords} unique keywords`);
    console.log(`🔥 Top keywords:`, keywordSummary.topKeywords.slice(0, 10).map(k => k.keyword));
    console.log();

    // 3. 展示所有可用关键词（供LLM选择）
    console.log('📋 Available Keywords for LLM Selection:');
    const categorizedKeywords = optimizer.getKeywordsByCategory();
    
    console.log('Components:', categorizedKeywords.components.slice(0, 5));
    console.log('Properties:', categorizedKeywords.properties.slice(0, 5));
    console.log('Colors:', categorizedKeywords.colors.slice(0, 5));
    console.log('Sizes:', categorizedKeywords.sizes.slice(0, 5));
    console.log();

    // 4. 模拟LLM根据用户查询选择关键词
    console.log('🤖 LLM Keyword Selection Simulation:');
    
    const userQueries = [
      '我想查看按钮的大小',
      '显示所有图标相关的组件',
      '找出粉色的按钮'
    ];

    for (const query of userQueries) {
      console.log(`\nUser Query: "${query}"`);
      const suggestions = optimizer.getKeywordSuggestionsForLLM(query);
      console.log('Recommended keywords:', suggestions.recommended);
      console.log('Related keywords:', suggestions.related.slice(0, 5));
    }
    console.log();

    // 5. 演示关键词过滤功能
    console.log('🔍 Keyword Filtering Demonstrations:\n');

    // 示例1：查询按钮相关数据
    console.log('Example 1: Filter by "按钮" (Button)');
    const buttonFilter = optimizer.filterByKeywords(['按钮'], {
      matchMode: 'any',
      includePartialMatch: true
    });
    
    console.log(`Results: ${Object.keys(buttonFilter.data.metadata.components).length} components`);
    console.log(`Data reduction: ${buttonFilter.stats.reductionPercentage.components}%`);
    console.log();

    // 示例2：查询特定尺寸的按钮
    console.log('Example 2: Filter by "按钮" + "小" (Small Buttons)');
    const smallButtonFilter = optimizer.filterByKeywords(['按钮', '小'], {
      matchMode: 'all',
      includePartialMatch: true
    });
    
    console.log(`Results: ${Object.keys(smallButtonFilter.data.metadata.components).length} components`);
    console.log(`Data reduction: ${smallButtonFilter.stats.reductionPercentage.components}%`);
    console.log();

    // 示例3：查询颜色相关数据
    console.log('Example 3: Filter by "颜色" (Color)');
    const colorFilter = optimizer.filterByKeywords(['颜色'], {
      matchMode: 'any',
      includePartialMatch: true
    });
    
    console.log(`Results: ${Object.keys(colorFilter.data.metadata.components).length} components`);
    console.log(`Data reduction: ${colorFilter.stats.reductionPercentage.components}%`);
    console.log();

    // 6. 生成优化报告
    console.log('📊 Optimization Report for Button Filter:');
    const report = optimizer.generateOptimizationReport(buttonFilter);
    console.log('Data Size Reduction:', report.dataReduction.dataSize);
    console.log('Component Reduction:', report.dataReduction.components);
    console.log('Recommendations:', report.recommendations);
    console.log();

    // 7. 智能关键词推荐
    console.log('💡 Smart Keyword Suggestions:');
    const selectedKeywords = ['按钮'];
    const suggestions = optimizer.suggestKeywords(selectedKeywords);
    console.log(`Based on "${selectedKeywords.join(', ')}", suggested keywords:`, suggestions);
    console.log();

    // 8. 搜索关键词功能
    console.log('🔎 Keyword Search:');
    const searchResults = optimizer.searchKeywords('按');
    console.log(`Search for "按":`, searchResults.slice(0, 10));
    console.log();

    // 9. 展示优化器状态
    console.log('📈 Optimizer Status:');
    const status = optimizer.getStatus();
    console.log(status);

    console.log('\n🎉 Simple usage demo completed successfully!');
    console.log('\n💡 Integration with LLM:');
    console.log('1. LLM receives user query (e.g., "查询按钮大小")');
    console.log('2. LLM calls getKeywordSuggestionsForLLM() to get keyword options');
    console.log('3. LLM selects relevant keywords (e.g., ["按钮", "尺寸"])');
    console.log('4. LLM calls filterByKeywords() with selected keywords');
    console.log('5. Return optimized data with significant size reduction');

    return optimizer;

  } catch (error) {
    console.error('❌ Demo failed:', error.message);
    throw error;
  }
}

/**
 * LLM集成示例
 * 演示如何与LLM集成使用
 */
async function llmIntegrationExample() {
  console.log('\n' + '='.repeat(60));
  console.log('🤖 LLM Integration Example\n');

  const optimizer = new FigmaTokenOptimizer();
  await optimizer.initialize('./figma.yml');

  // 模拟LLM处理流程
  const scenarios = [
    {
      userQuery: '我想看看所有按钮的样式',
      expectedKeywords: ['按钮', '样式']
    },
    {
      userQuery: '显示小尺寸的图标',
      expectedKeywords: ['图标', '小']
    },
    {
      userQuery: '查找粉色的组件',
      expectedKeywords: ['粉']
    }
  ];

  for (const scenario of scenarios) {
    console.log(`\n📝 Scenario: "${scenario.userQuery}"`);
    
    // Step 1: LLM获取关键词建议
    const suggestions = optimizer.getKeywordSuggestionsForLLM(scenario.userQuery);
    console.log('Available keywords:', suggestions.recommended.concat(suggestions.related).slice(0, 10));
    
    // Step 2: LLM选择关键词（这里模拟选择）
    const selectedKeywords = scenario.expectedKeywords;
    console.log('LLM selected keywords:', selectedKeywords);
    
    // Step 3: 执行过滤
    const result = optimizer.filterByKeywords(selectedKeywords, {
      matchMode: 'any',
      includePartialMatch: true
    });
    
    // Step 4: 返回结果
    console.log(`✅ Filtered data: ${Object.keys(result.data.metadata.components).length} components`);
    console.log(`📉 Size reduction: ${result.stats.reductionPercentage.components}% components, ${result.stats.reductionPercentage.nodes}% nodes`);
    
    // Step 5: 生成简化的响应数据
    const responseData = {
      query: scenario.userQuery,
      keywords: selectedKeywords,
      results: {
        componentCount: Object.keys(result.data.metadata.components).length,
        sampleComponents: Object.values(result.data.metadata.components).slice(0, 3).map(c => ({
          id: c.id,
          name: c.name
        }))
      },
      optimization: {
        dataReduction: result.stats.reductionPercentage.components + '%',
        originalSize: result.stats.original.components,
        filteredSize: result.stats.filtered.components
      }
    };
    
    console.log('📤 Response to user:', JSON.stringify(responseData, null, 2));
  }

  console.log('\n✨ LLM Integration Benefits:');
  console.log('• Dramatically reduced data transfer (50-90% reduction typical)');
  console.log('• Fast keyword-based filtering');
  console.log('• Intelligent keyword suggestions');
  console.log('• Simple API for LLM integration');
  console.log('• No complex indexing or caching required');
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    try {
      await simpleUsageDemo();
      await llmIntegrationExample();
    } catch (error) {
      console.error('Demo failed:', error);
      process.exit(1);
    }
  })();
}

export { simpleUsageDemo, llmIntegrationExample };
