import { createOptimizer, createMcpAdapter } from '../src/index.js';

/**
 * 基础使用示例
 * 演示如何使用FigmaOptimizer进行常见的查询操作
 */
async function basicUsageExample() {
  console.log('🚀 Basic Usage Example\n');

  // 1. 创建优化器实例
  const optimizer = createOptimizer({
    enableCache: true,
    enableCompression: true,
    cacheOptions: {
      stdTTL: 3600, // 1小时缓存
      checkperiod: 600 // 10分钟检查周期
    }
  });

  try {
    // 2. 初始化数据
    console.log('📊 Loading Figma data...');
    await optimizer.initialize('./figma.yml');
    console.log('✅ Data loaded successfully\n');

    // 3. 查询按钮大小信息
    console.log('🔍 Querying button sizes...');
    const buttonSizes = await optimizer.getButtonSizes();
    console.log('Button sizes available:', Object.keys(buttonSizes));
    
    // 显示每种尺寸的按钮数量
    for (const [size, buttons] of Object.entries(buttonSizes)) {
      console.log(`  ${size}: ${buttons.length} buttons`);
    }
    console.log();

    // 4. 查询按钮颜色信息
    console.log('🎨 Querying button colors...');
    const buttonColors = await optimizer.getButtonColors();
    console.log('Button colors available:', Object.keys(buttonColors));
    
    for (const [color, buttons] of Object.entries(buttonColors)) {
      console.log(`  ${color}: ${buttons.length} buttons`);
    }
    console.log();

    // 5. 执行结构化查询
    console.log('📋 Executing structured queries...');
    
    // 查询所有小按钮
    const smallButtons = await optimizer.query({
      type: 'component',
      category: 'button',
      property: 'size',
      value: '小'
    });
    console.log(`Small buttons found: ${smallButtons.length}`);

    // 查询粉色按钮
    const pinkButtons = await optimizer.query({
      type: 'component',
      category: 'button',
      property: 'color',
      value: '粉'
    });
    console.log(`Pink buttons found: ${pinkButtons.length}`);

    // 查询实色样式的按钮
    const solidButtons = await optimizer.query({
      type: 'component',
      category: 'button',
      property: 'style',
      value: '实色'
    });
    console.log(`Solid style buttons found: ${solidButtons.length}\n`);

    // 6. 搜索功能
    console.log('🔎 Testing search functionality...');
    const searchResults = await optimizer.search('按钮', { limit: 5 });
    console.log(`Search results for "按钮": ${searchResults.length} items`);
    
    searchResults.slice(0, 3).forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.name} (${item.id})`);
    });
    console.log();

    // 7. 获取统计信息
    console.log('📈 Getting component statistics...');
    const stats = await optimizer.getComponentStats();
    console.log('Component Statistics:');
    console.log(`  Total components: ${stats.totalComponents}`);
    console.log(`  Buttons by size:`, stats.buttonsBySize);
    console.log(`  Buttons by color:`, stats.buttonsByColor);
    console.log(`  Buttons by style:`, stats.buttonsByStyle);
    console.log();

    // 8. 获取优化统计信息
    console.log('⚡ Getting optimization statistics...');
    const optimizationStats = optimizer.getOptimizationStats();
    console.log('Optimization Statistics:');
    console.log(`  Load time: ${optimizationStats.loadTime}ms`);
    console.log(`  Total queries: ${optimizationStats.totalQueries}`);
    console.log(`  Cache hit rate: ${(optimizationStats.cacheHitRate * 100).toFixed(2)}%`);
    console.log(`  Data stats:`, {
      totalComponents: optimizationStats.dataStats.totalComponents,
      totalNodes: optimizationStats.dataStats.totalNodes,
      indexedProperties: optimizationStats.dataStats.indexedProperties
    });

    return optimizer;

  } catch (error) {
    console.error('❌ Error in basic usage example:', error);
    throw error;
  }
}

/**
 * MCP适配器使用示例
 * 演示如何使用MCP适配器处理各种类型的查询
 */
async function mcpAdapterExample() {
  console.log('\n' + '='.repeat(50));
  console.log('🔌 MCP Adapter Example\n');

  // 1. 创建MCP适配器
  const adapter = createMcpAdapter({
    enableCache: true,
    enableCompression: true
  });

  try {
    // 2. 初始化适配器
    console.log('🔧 Initializing MCP adapter...');
    await adapter.initialize('./figma.yml');
    console.log('✅ MCP adapter initialized\n');

    // 3. 自然语言查询示例
    console.log('💬 Natural Language Queries:');
    
    const nlQueries = [
      '按钮大小',
      '按钮颜色',
      '小按钮',
      '粉色按钮',
      '大按钮'
    ];

    for (const query of nlQueries) {
      const result = await adapter.handleRequest({
        method: 'nlQuery',
        params: { query }
      });
      
      if (result.success) {
        const resultCount = Array.isArray(result.data) ? result.data.length : 
                          typeof result.data === 'object' ? Object.keys(result.data).length : 1;
        console.log(`  "${query}" -> ${resultCount} results`);
      } else {
        console.log(`  "${query}" -> Error: ${result.error}`);
      }
    }
    console.log();

    // 4. 结构化查询示例
    console.log('📋 Structured Queries:');
    
    const structuredQueries = [
      {
        name: 'All buttons',
        params: { type: 'component', category: 'button' }
      },
      {
        name: 'Button colors',
        params: { type: 'component', category: 'button', property: 'color' }
      },
      {
        name: 'Medium buttons',
        params: { type: 'component', category: 'button', property: 'size', value: '中' }
      }
    ];

    for (const query of structuredQueries) {
      const result = await adapter.handleRequest({
        method: 'query',
        params: query.params
      });
      
      if (result.success) {
        const resultCount = Array.isArray(result.data) ? result.data.length : 
                          typeof result.data === 'object' ? Object.keys(result.data).length : 1;
        console.log(`  ${query.name} -> ${resultCount} results`);
      }
    }
    console.log();

    // 5. 搜索查询示例
    console.log('🔍 Search Queries:');
    
    const searchQueries = ['按钮', '图标', '文字'];
    
    for (const keyword of searchQueries) {
      const result = await adapter.handleRequest({
        method: 'search',
        params: { keyword, options: { limit: 5 } }
      });
      
      if (result.success) {
        console.log(`  "${keyword}" -> ${result.data.length} results`);
      }
    }
    console.log();

    // 6. 批量查询示例
    console.log('📦 Batch Queries:');
    
    const batchRequests = [
      { method: 'nlQuery', params: { query: '小按钮' } },
      { method: 'search', params: { keyword: '按钮', options: { limit: 3 } } },
      { method: 'getStats', params: { type: 'components' } }
    ];

    const batchResults = await adapter.handleBatchRequests(batchRequests);
    console.log(`  Batch of ${batchRequests.length} requests -> ${batchResults.filter(r => r.success).length} successful`);
    console.log();

    // 7. 获取适配器状态
    console.log('📊 Adapter Status:');
    const status = adapter.getStatus();
    console.log(`  Initialized: ${status.initialized}`);
    console.log(`  Supported methods: ${status.supportedMethods.join(', ')}`);
    console.log(`  Query patterns: ${status.queryPatterns.length}`);

    return adapter;

  } catch (error) {
    console.error('❌ Error in MCP adapter example:', error);
    throw error;
  }
}

/**
 * 高级功能示例
 * 演示数据压缩、缓存优化等高级功能
 */
async function advancedFeaturesExample(optimizer) {
  console.log('\n' + '='.repeat(50));
  console.log('⚡ Advanced Features Example\n');

  try {
    // 1. 数据压缩示例
    console.log('🗜️ Data Compression:');
    
    const testData = await optimizer.getComponentStats();
    const compressed = optimizer.compressData(testData);
    const decompressed = optimizer.decompressData(compressed);
    
    const originalSize = JSON.stringify(testData).length;
    const compressedSize = compressed.length;
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
    
    console.log(`  Original size: ${originalSize} bytes`);
    console.log(`  Compressed size: ${compressedSize} bytes`);
    console.log(`  Compression ratio: ${compressionRatio}%`);
    console.log(`  Data integrity: ${JSON.stringify(testData) === JSON.stringify(decompressed) ? '✅ Preserved' : '❌ Lost'}`);
    console.log();

    // 2. 缓存性能测试
    console.log('💾 Cache Performance:');
    
    const startTime = Date.now();
    
    // 第一次查询（无缓存）
    await optimizer.getButtonSizes();
    const firstQueryTime = Date.now() - startTime;
    
    const cachedStartTime = Date.now();
    
    // 第二次查询（有缓存）
    await optimizer.getButtonSizes();
    const cachedQueryTime = Date.now() - cachedStartTime;
    
    console.log(`  First query (no cache): ${firstQueryTime}ms`);
    console.log(`  Second query (cached): ${cachedQueryTime}ms`);
    console.log(`  Speed improvement: ${(firstQueryTime / cachedQueryTime).toFixed(2)}x`);
    console.log();

    // 3. 导出优化数据
    console.log('📤 Data Export:');
    
    const exportedData = optimizer.exportOptimizedData({
      includeNodes: false,
      compress: true
    });
    
    console.log(`  Export format: ${exportedData.compressed ? 'Compressed' : 'Raw'}`);
    console.log(`  Export size: ${exportedData.data.length} bytes`);
    if (exportedData.compressionStats) {
      console.log(`  Compression ratio: ${exportedData.compressionStats.compressionRatio.toFixed(2)}%`);
    }

  } catch (error) {
    console.error('❌ Error in advanced features example:', error);
    throw error;
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runExamples() {
  try {
    console.log('🎯 Figma Token Optimizer - Usage Examples\n');
    
    // 运行基础使用示例
    const optimizer = await basicUsageExample();
    
    // 运行MCP适配器示例
    const adapter = await mcpAdapterExample();
    
    // 运行高级功能示例
    await advancedFeaturesExample(optimizer);
    
    console.log('\n🎉 All examples completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('  1. Integrate with your LLM application');
    console.log('  2. Customize query patterns for your use case');
    console.log('  3. Optimize cache settings for your data size');
    console.log('  4. Monitor performance and adjust compression settings');
    
    // 清理资源
    optimizer.destroy();
    adapter.destroy();
    
  } catch (error) {
    console.error('❌ Examples failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}

export { basicUsageExample, mcpAdapterExample, advancedFeaturesExample };
