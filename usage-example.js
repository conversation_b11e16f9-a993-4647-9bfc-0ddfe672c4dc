import { extractKeywords, filterFigmaData } from './simple-optimizer.js';

/**
 * 使用示例：展示如何与LLM集成
 */
async function usageExample() {
  console.log('🎯 Simple Figma Optimizer - Usage Example\n');

  try {
    // ========== 步骤1: 获取所有关键词供LLM选择 ==========
    console.log('📋 Step 1: Get all keywords for LLM');
    const allKeywords = await extractKeywords('./figma.yml');
    console.log(`✅ Extracted ${allKeywords.length} keywords from figma.yml`);
    console.log('🔤 Available keywords:', allKeywords.slice(0, 15), '...\n');

    // ========== 步骤2: 模拟不同的用户查询场景 ==========
    const testScenarios = [
      {
        userQuery: '我想查看所有按钮组件',
        llmSelectedKeywords: ['按钮', 'button']
      },
      {
        userQuery: '显示小尺寸的组件',
        llmSelectedKeywords: ['小', '尺寸']
      },
      {
        userQuery: '查找粉色相关的元素',
        llmSelectedKeywords: ['粉']
      },
      {
        userQuery: '我需要图标组件',
        llmSelectedKeywords: ['图标']
      }
    ];

    console.log('🤖 Step 2: Test different user scenarios\n');

    for (let i = 0; i < testScenarios.length; i++) {
      const scenario = testScenarios[i];
      
      console.log(`📝 Scenario ${i + 1}:`);
      console.log(`   User Query: "${scenario.userQuery}"`);
      console.log(`   LLM Selected: [${scenario.llmSelectedKeywords.join(', ')}]`);

      // 使用函数2过滤数据
      const filteredData = await filterFigmaData('./figma.yml', scenario.llmSelectedKeywords);
      
      // 计算优化效果
      const originalComponents = 44; // 已知原始组件数
      const filteredComponents = Object.keys(filteredData.metadata.components).length;
      const reduction = ((originalComponents - filteredComponents) / originalComponents * 100).toFixed(2);
      const dataSize = Math.round(JSON.stringify(filteredData).length / 1024);
      
      console.log(`   ✅ Result: ${filteredComponents}/${originalComponents} components (${reduction}% reduction, ${dataSize}KB)`);
      
      // 显示一些示例组件
      const sampleComponents = Object.values(filteredData.metadata.components).slice(0, 2);
      if (sampleComponents.length > 0) {
        console.log('   📋 Sample results:');
        sampleComponents.forEach((comp, idx) => {
          console.log(`      ${idx + 1}. ${comp.name}`);
        });
      }
      console.log();
    }

    // ========== 步骤3: 展示实际集成代码 ==========
    console.log('💻 Step 3: Integration code example\n');
    
    console.log('```javascript');
    console.log('// 在您的LLM应用中的集成代码：');
    console.log('');
    console.log('import { extractKeywords, filterFigmaData } from "./simple-optimizer.js";');
    console.log('');
    console.log('// 1. 获取所有关键词供LLM选择');
    console.log('const keywords = await extractKeywords("./figma.yml");');
    console.log('// 将keywords发送给LLM，让LLM根据用户问题选择相关的关键词');
    console.log('');
    console.log('// 2. LLM根据用户查询选择关键词');
    console.log('const userQuery = "我想查看按钮的大小";');
    console.log('const llmSelectedKeywords = ["按钮", "尺寸"]; // LLM的选择结果');
    console.log('');
    console.log('// 3. 根据选中的关键词过滤数据');
    console.log('const optimizedData = await filterFigmaData("./figma.yml", llmSelectedKeywords);');
    console.log('// 返回优化后的数据给用户');
    console.log('```');
    console.log();

    // ========== 总结 ==========
    console.log('🎉 Summary:');
    console.log('✅ Only 2 functions needed:');
    console.log('   1. extractKeywords() - Get all keywords for LLM');
    console.log('   2. filterFigmaData() - Filter data by LLM-selected keywords');
    console.log('✅ Typical data reduction: 40-80%');
    console.log('✅ Zero dependencies, ready to use');
    console.log('✅ Perfect for LLM integration');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// 运行示例
usageExample();
