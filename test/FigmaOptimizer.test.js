import { test, describe } from 'node:test';
import assert from 'node:assert';
import { FigmaOptimizer } from '../src/core/FigmaOptimizer.js';
import { McpAdapter } from '../src/adapters/McpAdapter.js';

describe('FigmaOptimizer', () => {
  let optimizer;
  
  test('should initialize successfully', async () => {
    optimizer = new FigmaOptimizer({
      enableCache: true,
      enableCompression: true
    });
    
    await optimizer.initialize('./figma.yml');
    assert.strictEqual(optimizer.isInitialized, true);
  });

  test('should get button sizes', async () => {
    const buttonSizes = await optimizer.getButtonSizes();
    assert(typeof buttonSizes === 'object');
    assert(Object.keys(buttonSizes).length > 0);
    console.log('Button sizes found:', Object.keys(buttonSizes));
  });

  test('should get button colors', async () => {
    const buttonColors = await optimizer.getButtonColors();
    assert(typeof buttonColors === 'object');
    assert(Object.keys(buttonColors).length > 0);
    console.log('Button colors found:', Object.keys(buttonColors));
  });

  test('should perform search', async () => {
    const results = await optimizer.search('按钮', { limit: 5 });
    assert(Array.isArray(results));
    assert(results.length > 0);
    console.log(`Search results count: ${results.length}`);
  });

  test('should get component stats', async () => {
    const stats = await optimizer.getComponentStats();
    assert(typeof stats === 'object');
    assert(typeof stats.totalComponents === 'number');
    assert(stats.totalComponents > 0);
    console.log('Component stats:', {
      totalComponents: stats.totalComponents,
      buttonsBySize: Object.keys(stats.buttonsBySize || {}),
      buttonsByColor: Object.keys(stats.buttonsByColor || {})
    });
  });

  test('should execute structured query', async () => {
    const result = await optimizer.query({
      type: 'component',
      category: 'button',
      property: 'size',
      value: '小'
    });
    
    assert(Array.isArray(result));
    console.log(`Small buttons found: ${result.length}`);
  });

  test('should get optimization stats', async () => {
    const stats = optimizer.getOptimizationStats();
    assert(typeof stats === 'object');
    assert(typeof stats.loadTime === 'number');
    assert(typeof stats.totalQueries === 'number');
    console.log('Optimization stats:', {
      loadTime: stats.loadTime,
      totalQueries: stats.totalQueries,
      cacheHitRate: stats.cacheHitRate
    });
  });

  test('should compress and decompress data', async () => {
    const testData = { test: 'data', array: [1, 2, 3], nested: { key: 'value' } };
    const compressed = optimizer.compressData(testData);
    const decompressed = optimizer.decompressData(compressed);
    
    assert(typeof compressed === 'string');
    assert.deepStrictEqual(decompressed, testData);
    console.log('Compression test passed');
  });

  test('should export optimized data', async () => {
    const exportedData = optimizer.exportOptimizedData({
      includeNodes: false,
      compress: true
    });
    
    assert(typeof exportedData === 'object');
    assert(exportedData.compressed === true);
    assert(typeof exportedData.data === 'string');
    console.log('Export test passed');
  });
});

describe('McpAdapter', () => {
  let adapter;

  test('should initialize MCP adapter', async () => {
    adapter = new McpAdapter({
      enableCache: true,
      enableCompression: true
    });
    
    await adapter.initialize('./figma.yml');
    assert.strictEqual(adapter.isInitialized, true);
  });

  test('should handle natural language query', async () => {
    const result = await adapter.handleRequest({
      method: 'nlQuery',
      params: { query: '按钮大小' }
    });
    
    assert.strictEqual(result.success, true);
    assert(result.data);
    assert.strictEqual(result.metadata.queryType, 'naturalLanguage');
    console.log('Natural language query test passed');
  });

  test('should handle structured query', async () => {
    const result = await adapter.handleRequest({
      method: 'query',
      params: {
        type: 'component',
        category: 'button',
        property: 'color'
      }
    });
    
    assert.strictEqual(result.success, true);
    assert(result.data);
    assert.strictEqual(result.metadata.queryType, 'structured');
    console.log('Structured query test passed');
  });

  test('should handle search request', async () => {
    const result = await adapter.handleRequest({
      method: 'search',
      params: {
        keyword: '按钮',
        options: { limit: 3 }
      }
    });
    
    assert.strictEqual(result.success, true);
    assert(Array.isArray(result.data));
    assert.strictEqual(result.metadata.queryType, 'search');
    console.log('Search request test passed');
  });

  test('should handle stats request', async () => {
    const result = await adapter.handleRequest({
      method: 'getStats',
      params: { type: 'components' }
    });
    
    assert.strictEqual(result.success, true);
    assert(result.data);
    assert.strictEqual(result.metadata.queryType, 'stats');
    console.log('Stats request test passed');
  });

  test('should handle batch requests', async () => {
    const requests = [
      {
        method: 'nlQuery',
        params: { query: '小按钮' }
      },
      {
        method: 'search',
        params: { keyword: '按钮', options: { limit: 2 } }
      }
    ];
    
    const results = await adapter.handleBatchRequests(requests);
    assert(Array.isArray(results));
    assert.strictEqual(results.length, 2);
    assert(results.every(r => r.success));
    console.log('Batch requests test passed');
  });

  test('should get adapter status', async () => {
    const status = adapter.getStatus();
    assert(typeof status === 'object');
    assert.strictEqual(status.initialized, true);
    assert(Array.isArray(status.supportedMethods));
    assert(status.supportedMethods.length > 0);
    console.log('Adapter status:', {
      initialized: status.initialized,
      supportedMethods: status.supportedMethods.length,
      queryPatterns: status.queryPatterns.length
    });
  });

  test('should parse natural language queries correctly', async () => {
    const testQueries = [
      { query: '按钮大小', expected: 'button:size' },
      { query: '按钮颜色', expected: 'button:color' },
      { query: '小按钮', expected: 'button:size:小' },
      { query: '粉色按钮', expected: 'button:color:粉' }
    ];

    for (const testCase of testQueries) {
      const parsedQuery = adapter.parseNaturalLanguageQuery(testCase.query);
      assert(parsedQuery !== null, `Failed to parse: ${testCase.query}`);
      console.log(`Parsed "${testCase.query}":`, parsedQuery);
    }
  });

  // 清理资源
  test('cleanup', async () => {
    if (optimizer) {
      optimizer.destroy();
    }
    if (adapter) {
      adapter.destroy();
    }
    console.log('Cleanup completed');
  });
});

// 性能测试
describe('Performance Tests', () => {
  let optimizer;

  test('should load data quickly', async () => {
    const startTime = Date.now();
    optimizer = new FigmaOptimizer({ enableCache: true });
    await optimizer.initialize('./figma.yml');
    const loadTime = Date.now() - startTime;
    
    console.log(`Data loading time: ${loadTime}ms`);
    assert(loadTime < 5000, 'Loading should complete within 5 seconds');
  });

  test('should query efficiently with cache', async () => {
    const startTime = Date.now();
    
    // 执行多次相同查询，测试缓存效果
    for (let i = 0; i < 10; i++) {
      await optimizer.getButtonSizes();
    }
    
    const totalTime = Date.now() - startTime;
    const avgTime = totalTime / 10;
    
    console.log(`Average query time (with cache): ${avgTime.toFixed(2)}ms`);
    assert(avgTime < 100, 'Cached queries should be very fast');
  });

  test('should handle concurrent queries', async () => {
    const startTime = Date.now();
    
    // 并发执行多个不同查询
    const promises = [
      optimizer.getButtonSizes(),
      optimizer.getButtonColors(),
      optimizer.search('按钮'),
      optimizer.getComponentStats(),
      optimizer.query({ type: 'component', category: 'button' })
    ];
    
    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;
    
    console.log(`Concurrent queries time: ${totalTime}ms`);
    assert(results.every(r => r !== null), 'All queries should return results');
    assert(totalTime < 3000, 'Concurrent queries should complete quickly');
  });

  test('cleanup performance tests', async () => {
    if (optimizer) {
      optimizer.destroy();
    }
  });
});
