# Figma Token Optimizer API 文档

## 概述

Figma Token Optimizer 提供了一套完整的API来优化Figma设计数据的查询和传输。主要包括以下核心类：

- `FigmaOptimizer` - 主优化器类
- `McpAdapter` - MCP协议适配器
- `DataParser` - 数据解析器
- `QueryEngine` - 查询引擎
- `CacheManager` - 缓存管理器
- `DataCompressor` - 数据压缩器

## FigmaOptimizer

### 构造函数

```javascript
const optimizer = new FigmaOptimizer(options)
```

**参数:**
- `options` (Object, 可选)
  - `enableCache` (Boolean) - 是否启用缓存，默认 `true`
  - `enableCompression` (Boolean) - 是否启用压缩，默认 `true`
  - `cacheOptions` (Object) - 缓存配置选项
  - `compressionMethod` (String) - 压缩方法，默认 `'optimized'`

### 主要方法

#### initialize(dataSource)

初始化优化器并加载数据。

```javascript
await optimizer.initialize('./figma.yml')
```

**参数:**
- `dataSource` (String|Object) - 数据源路径或数据对象

**返回:** Promise<void>

#### query(queryObject)

执行结构化查询。

```javascript
const result = await optimizer.query({
  type: 'component',
  category: 'button',
  property: 'size',
  value: '小'
})
```

**参数:**
- `queryObject` (Object)
  - `type` (String) - 查询类型: 'component', 'node', 'componentSet'
  - `category` (String, 可选) - 组件类别
  - `property` (String, 可选) - 属性名
  - `value` (String, 可选) - 属性值
  - `filters` (Object, 可选) - 额外过滤条件

**返回:** Promise<Array|Object>

#### getButtonSizes()

获取所有按钮尺寸信息的快捷方法。

```javascript
const buttonSizes = await optimizer.getButtonSizes()
// 返回: { '小': [...], '中': [...], '大': [...] }
```

**返回:** Promise<Object>

#### getButtonColors()

获取所有按钮颜色信息的快捷方法。

```javascript
const buttonColors = await optimizer.getButtonColors()
// 返回: { '粉': [...], '灰': [...], '黄': [...] }
```

**返回:** Promise<Object>

#### search(keyword, options)

搜索组件和节点。

```javascript
const results = await optimizer.search('按钮', { limit: 10 })
```

**参数:**
- `keyword` (String) - 搜索关键词
- `options` (Object, 可选)
  - `type` (String) - 搜索类型: 'all', 'component', 'node'
  - `limit` (Number) - 结果数量限制

**返回:** Promise<Array>

#### getComponentStats()

获取组件统计信息。

```javascript
const stats = await optimizer.getComponentStats()
```

**返回:** Promise<Object>

#### compressData(data) / decompressData(compressedData)

压缩和解压缩数据。

```javascript
const compressed = optimizer.compressData(data)
const decompressed = optimizer.decompressData(compressed)
```

#### getOptimizationStats()

获取优化统计信息。

```javascript
const stats = optimizer.getOptimizationStats()
```

**返回:** Object

## McpAdapter

### 构造函数

```javascript
const adapter = new McpAdapter(options)
```

### 主要方法

#### handleRequest(request)

处理MCP请求。

```javascript
const response = await adapter.handleRequest({
  method: 'nlQuery',
  params: { query: '按钮大小' }
})
```

**支持的方法:**
- `query` - 结构化查询
- `search` - 搜索查询
- `getComponent` - 获取组件信息
- `getStats` - 获取统计信息
- `nlQuery` - 自然语言查询

#### handleBatchRequests(requests)

批量处理请求。

```javascript
const results = await adapter.handleBatchRequests([
  { method: 'nlQuery', params: { query: '小按钮' } },
  { method: 'search', params: { keyword: '按钮' } }
])
```

## 查询示例

### 1. 基础组件查询

```javascript
// 查询所有按钮
const allButtons = await optimizer.query({
  type: 'component',
  category: 'button'
})

// 查询特定尺寸的按钮
const smallButtons = await optimizer.query({
  type: 'component',
  category: 'button',
  property: 'size',
  value: '小'
})
```

### 2. 复杂过滤查询

```javascript
// 查询粉色的小按钮
const pinkSmallButtons = await optimizer.query({
  type: 'component',
  category: 'button',
  filters: {
    properties: {
      size: '小',
      color: '粉'
    }
  }
})
```

### 3. 节点查询

```javascript
// 查询所有文本节点
const textNodes = await optimizer.query({
  type: 'node',
  category: 'type',
  value: 'TEXT'
})
```

### 4. 搜索查询

```javascript
// 模糊搜索
const searchResults = await optimizer.search('按钮', {
  type: 'component',
  limit: 20
})
```

## MCP自然语言查询

McpAdapter支持自然语言查询，可以理解以下模式：

```javascript
// 按钮相关查询
await adapter.handleRequest({
  method: 'nlQuery',
  params: { query: '按钮大小' }
})

await adapter.handleRequest({
  method: 'nlQuery',
  params: { query: '小按钮' }
})

await adapter.handleRequest({
  method: 'nlQuery',
  params: { query: '粉色按钮' }
})
```

## 响应格式

### 成功响应

```javascript
{
  success: true,
  data: [...], // 查询结果
  metadata: {
    queryType: 'structured',
    resultCount: 5,
    timestamp: '2024-01-01T00:00:00.000Z'
  }
}
```

### 错误响应

```javascript
{
  success: false,
  error: 'Error message',
  timestamp: '2024-01-01T00:00:00.000Z'
}
```

## 性能优化建议

### 1. 缓存配置

```javascript
const optimizer = new FigmaOptimizer({
  enableCache: true,
  cacheOptions: {
    stdTTL: 3600, // 1小时缓存
    checkperiod: 600, // 10分钟检查周期
    useClones: false // 提升性能
  }
})
```

### 2. 压缩设置

```javascript
const optimizer = new FigmaOptimizer({
  enableCompression: true,
  compressionMethod: 'optimized' // 最佳压缩效果
})
```

### 3. 查询优化

- 使用具体的查询条件而不是模糊搜索
- 利用缓存机制避免重复查询
- 使用批量查询减少网络开销

## 错误处理

```javascript
try {
  const result = await optimizer.query({
    type: 'component',
    category: 'button'
  })
} catch (error) {
  console.error('Query failed:', error.message)
}
```

## 资源管理

```javascript
// 清理缓存
optimizer.clearCache()

// 重新加载数据
await optimizer.reload()

// 销毁实例
optimizer.destroy()
```
